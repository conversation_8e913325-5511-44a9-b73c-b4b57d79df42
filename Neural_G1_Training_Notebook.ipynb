# PHASE 1: Install Compatible Packages (Fixed Version)
print("🔧 Installing compatible deep learning packages...")
print("🎯 Using PyTorch-focused approach for maximum stability")

# Fix NumPy/TensorFlow compatibility by uninstalling conflicting versions
!pip uninstall numpy tensorflow -y -q

# Install compatible versions in correct order
!pip install numpy==1.23.5
!pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Install core data science packages
!pip install pandas matplotlib seaborn scikit-learn
!pip install plotly tqdm ipywidgets

print("\n✅ Core packages installed successfully!")
print("📝 Continue to the next cell for specialized packages.")

# PHASE 2: Install Specialized ML Packages

print("🔧 Installing specialized ML packages...")

# Install transformers and advanced ML packages
!pip install transformers
!pip install yfinance

# Install PyTorch Lightning and related packages
!pip install pytorch-lightning
!pip install optuna
!pip install wandb

# Skip TA-Lib installation (will use pandas-based alternatives)
print("📊 Using pandas-based technical indicators (more reliable than TA-Lib)")

print("\n✅ All specialized packages installed successfully!")

# PHASE 3: Setup Environment and Mount Drive

# Enable widget extensions for Colab
try:
    from google.colab import output
    output.enable_custom_widget_manager()
    print("✅ Widget manager enabled")
except:
    print("⚠️ Widget manager not available (running outside Colab)")

# Mount Google Drive
try:
    from google.colab import drive
    drive.mount('/content/drive')
    print("✅ Google Drive mounted successfully!")
except:
    print("⚠️ Google Drive not available (running outside Colab)")

print("\n🎉 Environment setup complete!")
print("📝 Ready to proceed with Neural G1 training!")

# Updated imports with compatibility fixes
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Fix environment variables for compatibility
import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

# PyTorch (Primary Deep Learning Framework)
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torch.nn.functional as F

# Skip TensorFlow for now (compatibility issues)
# We'll use PyTorch for all models
print("🎯 Using PyTorch-only approach for maximum stability")

# Machine Learning
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix

# Visualization
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from IPython.display import display, clear_output, HTML
import ipywidgets as widgets

# Progress tracking
from tqdm.auto import tqdm
import time

# Utilities
import json
import pickle
from datetime import datetime, timedelta
import random

# Set random seeds for reproducibility
np.random.seed(42)
torch.manual_seed(42)
if torch.cuda.is_available():
    torch.cuda.manual_seed(42)

print("📦 All imports completed successfully!")
print(f"🔥 NumPy version: {np.__version__}")
print(f"🔥 PyTorch version: {torch.__version__}")
print(f"🖥️ CUDA Available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"🖥️ GPU Device: {torch.cuda.get_device_name(0)}")
print("✅ Using pandas-based technical indicators (no TA-Lib dependency)")

# Improved Data loading function for multiple timeframes
def load_forex_data_improved(file_path, timeframe):
    """
    Enhanced data loading function that handles various CSV formats
    """
    try:
        print(f"📂 Loading {timeframe} from {file_path}...")
        
        # Try different separators and encodings
        df = None
        for sep in [',', '\t', ';', '|']:
            for encoding in ['utf-8', 'latin-1', 'cp1252']:
                try:
                    df = pd.read_csv(file_path, sep=sep, encoding=encoding)
                    if len(df.columns) >= 5:  # At least OHLC + 1 more
                        print(f"✅ Read with separator '{sep}', encoding '{encoding}'")
                        break
                except:
                    continue
            if df is not None and len(df.columns) >= 5:
                break
        
        if df is None:
            raise ValueError("Could not read CSV file")
        
        print(f"📊 Original shape: {df.shape}, Columns: {list(df.columns)}")
        
        # Handle different column structures
        if len(df.columns) == 5:
            df.columns = ['DateTime', 'Open', 'High', 'Low', 'Close']
            df['Volume'] = 1000  # Add dummy volume
        elif len(df.columns) == 6:
            df.columns = ['DateTime', 'Open', 'High', 'Low', 'Close', 'Volume']
        elif len(df.columns) == 7:
            df.columns = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume']
        else:
            # Take first 6 columns
            df = df.iloc[:, :6]
            df.columns = ['DateTime', 'Open', 'High', 'Low', 'Close', 'Volume']
        
        # Handle DateTime creation
        if 'Date' in df.columns and 'Time' in df.columns:
            # Convert to string and combine
            df['Date'] = df['Date'].astype(str)
            df['Time'] = df['Time'].astype(str)
            df['DateTime'] = df['Date'] + ' ' + df['Time']
            df = df.drop(['Date', 'Time'], axis=1)
        
        # Convert DateTime
        df['DateTime'] = pd.to_datetime(df['DateTime'], errors='coerce')
        df = df.dropna(subset=['DateTime'])
        df.set_index('DateTime', inplace=True)
        
        # Process OHLCV columns
        ohlcv_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in ohlcv_cols:
            if col in df.columns:
                # Handle European decimal format
                if df[col].dtype == 'object':
                    df[col] = df[col].astype(str).str.replace(',', '.', regex=False)
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Clean data
        df = df[ohlcv_cols].dropna()
        df['Timeframe'] = timeframe
        
        if len(df) == 0:
            raise ValueError("No valid data after processing")
        
        print(f"✅ Loaded {timeframe}: {len(df)} rows, {df.index[0]} to {df.index[-1]}")
        return df
        
    except Exception as e:
        print(f"❌ Error loading {timeframe}: {str(e)}")
        return None

print("✅ Improved data loading function ready!")

# Debug: Check your data files
print("🔍 Checking your uploaded data files...")

data_dir = '/content/drive/MyDrive/Neural_G1/data/'
timeframes = ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1']

for tf in timeframes:
    file_path = f'{data_dir}XAUUSD_{tf}.csv'
    
    if os.path.exists(file_path):
        print(f"\n📁 Found: {file_path}")
        
        # Check file size
        file_size = os.path.getsize(file_path)
        print(f"📏 Size: {file_size:,} bytes")
        
        # Read first few lines
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()[:5]
                print(f"📄 First 5 lines:")
                for i, line in enumerate(lines):
                    print(f"  {i+1}: {line.strip()}")
        except Exception as e:
            print(f"❌ Error reading file: {e}")
            
        # Try to detect separator
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                first_line = f.readline().strip()
                if ',' in first_line:
                    sep = ','
                elif '\t' in first_line:
                    sep = '\t'
                elif ';' in first_line:
                    sep = ';'
                else:
                    sep = 'unknown'
                print(f"🔧 Detected separator: '{sep}'")
                print(f"📊 Columns detected: {len(first_line.split(sep))}")
        except:
            print("❌ Could not detect file structure")
    else:
        print(f"❌ Not found: {file_path}")

print("\n✅ File inspection complete!")

# Data loading function for multiple timeframes
def load_forex_data(file_path, timeframe):
    """
    Load and preprocess forex data from CSV files
    Expected format: Date, Time, Open, High, Low, Close, Volume
    """
    try:
        # Try different separators
        for sep in ['\t', ',', ';']:
            try:
                df = pd.read_csv(file_path, sep=sep)
                if len(df.columns) >= 6:
                    break
            except:
                continue
        
        # Standardize column names
        expected_cols = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume']
        if len(df.columns) >= 6:
            df.columns = expected_cols[:len(df.columns)]
        
        # Combine Date and Time if separate
        if 'Time' in df.columns:
            df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
        else:
            df['DateTime'] = pd.to_datetime(df['Date'])
        
        # Set DateTime as index
        df.set_index('DateTime', inplace=True)
        
        # Keep only OHLCV columns
        ohlcv_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        df = df[ohlcv_cols]
        
        # Convert to numeric
        for col in ohlcv_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Remove any rows with NaN values
        df.dropna(inplace=True)
        
        # Add timeframe identifier
        df['Timeframe'] = timeframe
        
        print(f"✅ Loaded {timeframe} data: {len(df)} rows, {df.index[0]} to {df.index[-1]}")
        return df
        
    except Exception as e:
        print(f"❌ Error loading {file_path}: {str(e)}")
        return None

# Sample data generation (if CSV files not available)
def generate_sample_data(timeframe, days=365):
    """
    Generate realistic XAUUSD sample data for testing
    """
    # Timeframe to minutes mapping
    tf_minutes = {
        'M1': 1, 'M5': 5, 'M15': 15, 'M30': 30,
        'H1': 60, 'H4': 240, 'D1': 1440
    }
    
    minutes = tf_minutes.get(timeframe, 15)
    total_periods = int((days * 24 * 60) / minutes)
    
    # Generate realistic price data
    np.random.seed(42)
    base_price = 2000.0  # XAUUSD base price
    
    # Generate price movements with trend and volatility
    returns = np.random.normal(0, 0.001, total_periods)  # Small random returns
    trend = np.sin(np.linspace(0, 4*np.pi, total_periods)) * 0.0005  # Cyclical trend
    prices = base_price * np.exp(np.cumsum(returns + trend))
    
    # Generate OHLC from prices
    data = []
    for i in range(len(prices)):
        if i == 0:
            open_price = prices[i]
        else:
            open_price = data[-1]['Close']
        
        close_price = prices[i]
        high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, 0.0005)))
        low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, 0.0005)))
        volume = np.random.randint(100, 1000)
        
        data.append({
            'Open': open_price,
            'High': high_price,
            'Low': low_price,
            'Close': close_price,
            'Volume': volume
        })
    
    # Create DataFrame with proper datetime index
    start_date = datetime.now() - timedelta(days=days)
    date_range = pd.date_range(start=start_date, periods=total_periods, freq=f'{minutes}min')
    
    df = pd.DataFrame(data, index=date_range)
    df['Timeframe'] = timeframe
    
    print(f"✅ Generated {timeframe} sample data: {len(df)} rows")
    return df

# Load or generate data for all timeframes
timeframes = ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1']
forex_data = {}

print("📊 Loading XAUUSD data for all timeframes...")

for tf in timeframes:
    # Try to load from CSV first
    csv_path = f'/content/drive/MyDrive/Neural_G1/data/XAUUSD_{tf}.csv'
    
    if os.path.exists(csv_path):
        forex_data[tf] = load_forex_data_improved(csv_path, tf)
    else:
        print(f"⚠️ CSV file not found for {tf}, generating sample data...")
        forex_data[tf] = generate_sample_data(tf, days=180)  # 6 months of data

print("\n📈 Data loading summary:")
for tf, df in forex_data.items():
    if df is not None:
        print(f"{tf}: {len(df):,} candles | {df.index[0].strftime('%Y-%m-%d')} to {df.index[-1].strftime('%Y-%m-%d')}")

# Pandas-based Technical Indicators (No TA-Lib dependency)
def calculate_technical_indicators_pandas(df):
    """
    Calculate comprehensive technical indicators using pandas only
    """
    df = df.copy()
    
    # Simple Moving Averages
    df['SMA_10'] = df['Close'].rolling(window=10).mean()
    df['SMA_20'] = df['Close'].rolling(window=20).mean()
    
    # Exponential Moving Averages
    df['EMA_5'] = df['Close'].ewm(span=5).mean()
    df['EMA_10'] = df['Close'].ewm(span=10).mean()
    df['EMA_20'] = df['Close'].ewm(span=20).mean()
    df['EMA_50'] = df['Close'].ewm(span=50).mean()
    
    # Bollinger Bands
    df['BB_Middle'] = df['Close'].rolling(window=20).mean()
    bb_std = df['Close'].rolling(window=20).std()
    df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
    df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)
    df['BB_Width'] = (df['BB_Upper'] - df['BB_Lower']) / df['BB_Middle']
    df['BB_Position'] = (df['Close'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])
    
    # RSI (Relative Strength Index)
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['RSI'] = 100 - (100 / (1 + rs))
    df['RSI_Oversold'] = (df['RSI'] < 30).astype(int)
    df['RSI_Overbought'] = (df['RSI'] > 70).astype(int)
    
    # MACD
    ema12 = df['Close'].ewm(span=12).mean()
    ema26 = df['Close'].ewm(span=26).mean()
    df['MACD'] = ema12 - ema26
    df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
    df['MACD_Hist'] = df['MACD'] - df['MACD_Signal']
    df['MACD_Bullish'] = (df['MACD'] > df['MACD_Signal']).astype(int)
    
    # Stochastic Oscillator
    low_14 = df['Low'].rolling(window=14).min()
    high_14 = df['High'].rolling(window=14).max()
    df['Stoch_K'] = 100 * ((df['Close'] - low_14) / (high_14 - low_14))
    df['Stoch_D'] = df['Stoch_K'].rolling(window=3).mean()
    
    # Average True Range (ATR)
    high_low = df['High'] - df['Low']
    high_close = np.abs(df['High'] - df['Close'].shift())
    low_close = np.abs(df['Low'] - df['Close'].shift())
    ranges = pd.concat([high_low, high_close, low_close], axis=1)
    true_range = ranges.max(axis=1)
    df['ATR'] = true_range.rolling(window=14).mean()
    
    # Price patterns
    df['Price_Change'] = df['Close'].pct_change()
    df['High_Low_Ratio'] = df['High'] / df['Low']
    df['Close_Open_Ratio'] = df['Close'] / df['Open']
    
    # Volume indicators
    df['Volume_SMA'] = df['Volume'].rolling(window=20).mean()
    df['Volume_Ratio'] = df['Volume'] / df['Volume_SMA']
    
    # ADX (Average Directional Index) - Simplified
    plus_dm = df['High'].diff()
    minus_dm = df['Low'].diff()
    plus_dm[plus_dm < 0] = 0
    minus_dm[minus_dm > 0] = 0
    minus_dm = minus_dm.abs()
    
    tr = true_range
    plus_di = 100 * (plus_dm.rolling(window=14).mean() / tr.rolling(window=14).mean())
    minus_di = 100 * (minus_dm.rolling(window=14).mean() / tr.rolling(window=14).mean())
    dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
    df['ADX'] = dx.rolling(window=14).mean()
    
    # CCI (Commodity Channel Index)
    typical_price = (df['High'] + df['Low'] + df['Close']) / 3
    sma_tp = typical_price.rolling(window=14).mean()
    mad = typical_price.rolling(window=14).apply(lambda x: np.abs(x - x.mean()).mean())
    df['CCI'] = (typical_price - sma_tp) / (0.015 * mad)
    
    return df

print("✅ Pandas-based technical indicators ready!")
print("📊 No TA-Lib dependency required!")

def calculate_technical_indicators(df):
    """
    Calculate comprehensive technical indicators for trading signals
    """
    df = df.copy()
    
    # Price-based indicators
    df['SMA_10'] = talib.SMA(df['Close'], timeperiod=10)
    df['SMA_20'] = talib.SMA(df['Close'], timeperiod=20)
    df['EMA_5'] = talib.EMA(df['Close'], timeperiod=5)
    df['EMA_10'] = talib.EMA(df['Close'], timeperiod=10)
    df['EMA_20'] = talib.EMA(df['Close'], timeperiod=20)
    df['EMA_50'] = talib.EMA(df['Close'], timeperiod=50)
    
    # Bollinger Bands
    df['BB_Upper'], df['BB_Middle'], df['BB_Lower'] = talib.BBANDS(df['Close'], timeperiod=20)
    df['BB_Width'] = (df['BB_Upper'] - df['BB_Lower']) / df['BB_Middle']
    df['BB_Position'] = (df['Close'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])
    
    # RSI
    df['RSI'] = talib.RSI(df['Close'], timeperiod=14)
    df['RSI_Oversold'] = (df['RSI'] < 30).astype(int)
    df['RSI_Overbought'] = (df['RSI'] > 70).astype(int)
    
    # MACD
    df['MACD'], df['MACD_Signal'], df['MACD_Hist'] = talib.MACD(df['Close'])
    df['MACD_Bullish'] = (df['MACD'] > df['MACD_Signal']).astype(int)
    
    # Stochastic
    df['Stoch_K'], df['Stoch_D'] = talib.STOCH(df['High'], df['Low'], df['Close'])
    
    # ATR for volatility
    df['ATR'] = talib.ATR(df['High'], df['Low'], df['Close'], timeperiod=14)
    
    # Price patterns
    df['Price_Change'] = df['Close'].pct_change()
    df['High_Low_Ratio'] = df['High'] / df['Low']
    df['Close_Open_Ratio'] = df['Close'] / df['Open']
    
    # Volume indicators
    df['Volume_SMA'] = talib.SMA(df['Volume'], timeperiod=20)
    df['Volume_Ratio'] = df['Volume'] / df['Volume_SMA']
    
    # Trend indicators
    df['ADX'] = talib.ADX(df['High'], df['Low'], df['Close'], timeperiod=14)
    df['CCI'] = talib.CCI(df['High'], df['Low'], df['Close'], timeperiod=14)
    
    return df

def identify_supply_demand_zones(df, window=20, strength_threshold=0.5):
    """
    Identify Supply and Demand zones - Primary framework for Neural G1
    """
    df = df.copy()
    
    # Calculate pivot points
    df['Pivot_High'] = df['High'].rolling(window=window, center=True).max() == df['High']
    df['Pivot_Low'] = df['Low'].rolling(window=window, center=True).min() == df['Low']
    
    # Supply zones (resistance levels)
    supply_zones = []
    demand_zones = []
    
    for i in range(window, len(df) - window):
        if df['Pivot_High'].iloc[i]:
            # Check zone strength based on volume and price action
            zone_high = df['High'].iloc[i]
            zone_low = df['Low'].iloc[i]
            zone_volume = df['Volume'].iloc[i]
            
            # Calculate zone strength
            avg_volume = df['Volume'].iloc[i-window:i+window].mean()
            volume_strength = zone_volume / avg_volume if avg_volume > 0 else 1
            
            if volume_strength > strength_threshold:
                supply_zones.append({
                    'index': i,
                    'high': zone_high,
                    'low': zone_low,
                    'strength': volume_strength,
                    'type': 'supply'
                })
        
        if df['Pivot_Low'].iloc[i]:
            # Demand zones (support levels)
            zone_high = df['High'].iloc[i]
            zone_low = df['Low'].iloc[i]
            zone_volume = df['Volume'].iloc[i]
            
            avg_volume = df['Volume'].iloc[i-window:i+window].mean()
            volume_strength = zone_volume / avg_volume if avg_volume > 0 else 1
            
            if volume_strength > strength_threshold:
                demand_zones.append({
                    'index': i,
                    'high': zone_high,
                    'low': zone_low,
                    'strength': volume_strength,
                    'type': 'demand'
                })
    
    # Add zone proximity features
    df['Near_Supply_Zone'] = 0.0
    df['Near_Demand_Zone'] = 0.0
    df['Zone_Strength'] = 0.0
    
    for zone in supply_zones:
        mask = (df.index >= df.index[zone['index'] - window]) & (df.index <= df.index[zone['index'] + window])
        df.loc[mask, 'Near_Supply_Zone'] = zone['strength']
        df.loc[mask, 'Zone_Strength'] = zone['strength']
    
    for zone in demand_zones:
        mask = (df.index >= df.index[zone['index'] - window]) & (df.index <= df.index[zone['index'] + window])
        df.loc[mask, 'Near_Demand_Zone'] = zone['strength']
        df.loc[mask, 'Zone_Strength'] = max(df.loc[mask, 'Zone_Strength'].max(), zone['strength'])
    
    return df, supply_zones, demand_zones

# Apply technical indicators to all timeframes
print("🔧 Calculating technical indicators for all timeframes...")

processed_data = {}
supply_demand_zones = {}

for tf, df in forex_data.items():
    if df is not None:
        print(f"Processing {tf}...")
        
        # Calculate technical indicators
        df_with_indicators = calculate_technical_indicators_pandas(df)
        
        # Identify supply/demand zones
        df_processed, supply_zones, demand_zones = identify_supply_demand_zones(df_with_indicators)
        
        processed_data[tf] = df_processed
        supply_demand_zones[tf] = {
            'supply': supply_zones,
            'demand': demand_zones
        }
        
        print(f"  ✅ {tf}: {len(supply_zones)} supply zones, {len(demand_zones)} demand zones")

print("\n📊 Technical indicators calculation completed!")

def generate_trading_signals(df, lookahead_periods=10, min_profit_pct=0.5, max_loss_pct=1.0):
    """
    Generate Buy/Sell signals based on future price movements and technical conditions
    """
    df = df.copy()
    
    # Initialize signal columns
    df['Signal'] = 0  # 0: No signal, 1: Buy, -1: Sell
    df['Entry_Price'] = df['Close']
    df['Take_Profit'] = 0.0
    df['Stop_Loss'] = 0.0
    df['Signal_Strength'] = 0.0
    
    for i in range(len(df) - lookahead_periods):
        current_price = df['Close'].iloc[i]
        future_prices = df['Close'].iloc[i+1:i+lookahead_periods+1]
        
        if len(future_prices) == 0:
            continue
        
        max_future_price = future_prices.max()
        min_future_price = future_prices.min()
        
        # Calculate potential profit/loss percentages
        max_profit_pct = ((max_future_price - current_price) / current_price) * 100
        max_loss_pct = ((current_price - min_future_price) / current_price) * 100
        
        # Get current technical conditions
        rsi = df['RSI'].iloc[i]
        macd_bullish = df['MACD_Bullish'].iloc[i]
        near_demand = df['Near_Demand_Zone'].iloc[i]
        near_supply = df['Near_Supply_Zone'].iloc[i]
        bb_position = df['BB_Position'].iloc[i]
        atr = df['ATR'].iloc[i]
        
        # Skip if essential indicators are NaN
        if pd.isna(rsi) or pd.isna(atr):
            continue
        
        # BUY Signal Conditions
        buy_conditions = [
            rsi < 35,  # Oversold
            macd_bullish == 1,  # MACD bullish
            near_demand > 0.5,  # Near demand zone
            bb_position < 0.2,  # Near lower Bollinger Band
            max_profit_pct >= min_profit_pct  # Profitable future movement
        ]
        
        # SELL Signal Conditions
        sell_conditions = [
            rsi > 65,  # Overbought
            macd_bullish == 0,  # MACD bearish
            near_supply > 0.5,  # Near supply zone
            bb_position > 0.8,  # Near upper Bollinger Band
            max_loss_pct >= min_profit_pct  # Profitable future movement (for sell)
        ]
        
        # Calculate signal strength
        buy_strength = sum(buy_conditions[:-1]) / (len(buy_conditions) - 1)
        sell_strength = sum(sell_conditions[:-1]) / (len(sell_conditions) - 1)
        
        # Generate BUY signal
        if sum(buy_conditions) >= 4:  # At least 4 conditions met
            df.loc[df.index[i], 'Signal'] = 1
            df.loc[df.index[i], 'Take_Profit'] = current_price * (1 + min_profit_pct/100)
            df.loc[df.index[i], 'Stop_Loss'] = current_price * (1 - max_loss_pct/100)
            df.loc[df.index[i], 'Signal_Strength'] = buy_strength
        
        # Generate SELL signal
        elif sum(sell_conditions) >= 4:  # At least 4 conditions met
            df.loc[df.index[i], 'Signal'] = -1
            df.loc[df.index[i], 'Take_Profit'] = current_price * (1 - min_profit_pct/100)
            df.loc[df.index[i], 'Stop_Loss'] = current_price * (1 + max_loss_pct/100)
            df.loc[df.index[i], 'Signal_Strength'] = sell_strength
    
    return df

# Generate signals for all timeframes
print("🎯 Generating trading signals for all timeframes...")

labeled_data = {}

for tf, df in processed_data.items():
    print(f"Generating signals for {tf}...")
    
    # Adjust lookahead based on timeframe
    lookahead_map = {
        'M1': 30, 'M5': 20, 'M15': 15, 'M30': 10,
        'H1': 8, 'H4': 5, 'D1': 3
    }
    
    lookahead = lookahead_map.get(tf, 10)
    df_labeled = generate_trading_signals(df, lookahead_periods=lookahead)
    
    # Remove rows with NaN values
    df_labeled = df_labeled.dropna()
    
    labeled_data[tf] = df_labeled
    
    # Print signal statistics
    buy_signals = (df_labeled['Signal'] == 1).sum()
    sell_signals = (df_labeled['Signal'] == -1).sum()
    total_signals = buy_signals + sell_signals
    
    print(f"  ✅ {tf}: {buy_signals} BUY, {sell_signals} SELL, {total_signals} total signals")
    print(f"     Signal rate: {(total_signals/len(df_labeled)*100):.2f}%")

print("\n🎯 Signal generation completed!")

# Temporal Fusion Transformer for Price Prediction
class TemporalFusionTransformer(nn.Module):
    def __init__(self, input_size, hidden_size=128, num_heads=8, num_layers=4, dropout=0.1):
        super(TemporalFusionTransformer, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        
        # Input projection
        self.input_projection = nn.Linear(input_size, hidden_size)
        
        # Positional encoding
        self.positional_encoding = nn.Parameter(torch.randn(1000, hidden_size))
        
        # Multi-head attention layers
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_size,
            nhead=num_heads,
            dim_feedforward=hidden_size * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # Output layers
        self.output_projection = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, 1)  # Price prediction
        )
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x):
        batch_size, seq_len, _ = x.shape
        
        # Input projection
        x = self.input_projection(x)
        
        # Add positional encoding
        pos_enc = self.positional_encoding[:seq_len, :].unsqueeze(0).expand(batch_size, -1, -1)
        x = x + pos_enc
        x = self.dropout(x)
        
        # Transformer encoding
        x = self.transformer(x)
        
        # Use last timestep for prediction
        x = x[:, -1, :]
        
        # Output projection
        output = self.output_projection(x)
        
        return output

# CNN + BiLSTM + Attention for Signal Generation
class CNNBiLSTMAttention(nn.Module):
    def __init__(self, input_size, hidden_size=128, num_classes=3, dropout=0.1):
        super(CNNBiLSTMAttention, self).__init__()
        
        # CNN layers for feature extraction
        self.conv1d_1 = nn.Conv1d(input_size, 64, kernel_size=3, padding=1)
        self.conv1d_2 = nn.Conv1d(64, 128, kernel_size=3, padding=1)
        self.conv1d_3 = nn.Conv1d(128, 64, kernel_size=3, padding=1)
        
        # BiLSTM layers
        self.bilstm = nn.LSTM(
            input_size=64,
            hidden_size=hidden_size,
            num_layers=2,
            batch_first=True,
            bidirectional=True,
            dropout=dropout
        )
        
        # Attention mechanism
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size * 2,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, num_classes)  # Buy, Sell, Hold
        )
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x):
        batch_size, seq_len, features = x.shape
        
        # CNN feature extraction
        x = x.transpose(1, 2)  # (batch, features, seq_len)
        x = torch.relu(self.conv1d_1(x))
        x = torch.relu(self.conv1d_2(x))
        x = torch.relu(self.conv1d_3(x))
        x = x.transpose(1, 2)  # (batch, seq_len, features)
        
        # BiLSTM
        lstm_out, _ = self.bilstm(x)
        
        # Self-attention
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        
        # Global average pooling
        pooled = torch.mean(attn_out, dim=1)
        
        # Classification
        output = self.classifier(pooled)
        
        return output

# Vision Transformer for Chart Pattern Recognition
class VisionTransformerPatterns(nn.Module):
    def __init__(self, image_size=224, patch_size=16, num_classes=10, dim=768, depth=12, heads=12, mlp_dim=3072):
        super(VisionTransformerPatterns, self).__init__()
        
        self.patch_size = patch_size
        self.num_patches = (image_size // patch_size) ** 2
        self.patch_dim = 3 * patch_size ** 2  # RGB channels
        
        # Patch embedding
        self.patch_embedding = nn.Linear(self.patch_dim, dim)
        
        # Positional embedding
        self.pos_embedding = nn.Parameter(torch.randn(1, self.num_patches + 1, dim))
        
        # Class token
        self.cls_token = nn.Parameter(torch.randn(1, 1, dim))
        
        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=dim,
            nhead=heads,
            dim_feedforward=mlp_dim,
            dropout=0.1,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=depth)
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.LayerNorm(dim),
            nn.Linear(dim, num_classes)
        )
    
    def forward(self, x):
        batch_size = x.shape[0]
        
        # Extract patches
        patches = self.extract_patches(x)
        
        # Patch embedding
        x = self.patch_embedding(patches)
        
        # Add class token
        cls_tokens = self.cls_token.expand(batch_size, -1, -1)
        x = torch.cat([cls_tokens, x], dim=1)
        
        # Add positional embedding
        x += self.pos_embedding
        
        # Transformer encoding
        x = self.transformer(x)
        
        # Classification using class token
        cls_output = x[:, 0]
        output = self.classifier(cls_output)
        
        return output
    
    def extract_patches(self, x):
        batch_size, channels, height, width = x.shape
        patches = x.unfold(2, self.patch_size, self.patch_size).unfold(3, self.patch_size, self.patch_size)
        patches = patches.contiguous().view(batch_size, channels, -1, self.patch_size, self.patch_size)
        patches = patches.permute(0, 2, 1, 3, 4).contiguous()
        patches = patches.view(batch_size, -1, channels * self.patch_size * self.patch_size)
        return patches

print("🧠 AI model architectures defined successfully!")

# Test the updated setup
print("🧪 Testing Neural G1 setup...")

# Test data generation
test_data = generate_sample_data('M15', days=30)
print(f"✅ Sample data generation: {len(test_data)} rows")

# Test technical indicators
test_indicators = calculate_technical_indicators_pandas(test_data)
print(f"✅ Technical indicators: {len(test_indicators.columns)} features")

# Test model initialization
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"✅ Device: {device}")

# Test TFT model
input_size = 20  # Number of features
tft_model = TemporalFusionTransformer(input_size=input_size)
print(f"✅ TFT Model: {sum(p.numel() for p in tft_model.parameters())} parameters")

# Test CNN-BiLSTM model
cnn_model = CNNBiLSTMAttention(input_size=input_size)
print(f"✅ CNN-BiLSTM Model: {sum(p.numel() for p in cnn_model.parameters())} parameters")

print("\n🎉 All tests passed! Neural G1 is ready for training!")
print("\n📝 Next steps:")
print("1. Upload your XAUUSD data to Google Drive")
print("2. Run the data loading cells")
print("3. Execute the training pipeline")
print("4. Monitor training progress with built-in visualizations")

# Siamese Network for Pattern Similarity Matching
class SiameseNetwork(nn.Module):
    def __init__(self, input_size, embedding_dim=128):
        super(SiameseNetwork, self).__init__()
        
        # Shared encoder network
        self.encoder = nn.Sequential(
            nn.Linear(input_size, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, embedding_dim),
            nn.Tanh()  # Normalize embeddings
        )
        
        # Similarity classifier
        self.similarity_classifier = nn.Sequential(
            nn.Linear(embedding_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )
    
    def forward_one(self, x):
        """Forward pass for one input"""
        return self.encoder(x)
    
    def forward(self, x1, x2):
        """Forward pass for pair of inputs"""
        # Get embeddings
        embedding1 = self.forward_one(x1)
        embedding2 = self.forward_one(x2)
        
        # Calculate absolute difference
        diff = torch.abs(embedding1 - embedding2)
        
        # Similarity score
        similarity = self.similarity_classifier(diff)
        
        return similarity, embedding1, embedding2

# Confidence Synthesizer - Final Decision Engine
class ConfidenceSynthesizer(nn.Module):
    def __init__(self, input_features=10, hidden_size=64, num_classes=3):
        super(ConfidenceSynthesizer, self).__init__()
        
        self.feature_processor = nn.Sequential(
            nn.Linear(input_features, hidden_size),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_size),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # Final decision layer
        self.decision_layer = nn.Sequential(
            nn.Linear(hidden_size // 2, num_classes),
            nn.Softmax(dim=1)
        )
        
        # Confidence estimation
        self.confidence_estimator = nn.Sequential(
            nn.Linear(hidden_size // 2, 16),
            nn.ReLU(),
            nn.Linear(16, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        # Process input features
        features = self.feature_processor(x)
        
        # Get decision probabilities
        decision_probs = self.decision_layer(features)
        
        # Get confidence score
        confidence = self.confidence_estimator(features)
        
        return decision_probs, confidence

print("🔗 Siamese Networks and Confidence Synthesizer defined!")

def prepare_sequences(df, sequence_length=60, target_col='Close'):
    """
    Prepare sequences for time series modeling
    """
    # Select feature columns (exclude target and non-numeric columns)
    feature_cols = [col for col in df.columns if col not in [
        'Signal', 'Entry_Price', 'Take_Profit', 'Stop_Loss', 'Timeframe',
        'Pivot_High', 'Pivot_Low'
    ] and df[col].dtype in ['float64', 'int64']]
    
    # Fill any remaining NaN values
    df_clean = df[feature_cols + ['Signal']].fillna(method='ffill').fillna(0)
    
    # Normalize features
    scaler = StandardScaler()
    scaled_features = scaler.fit_transform(df_clean[feature_cols])
    
    # Create sequences
    X, y_price, y_signal = [], [], []
    
    for i in range(sequence_length, len(scaled_features)):
        # Input sequence
        X.append(scaled_features[i-sequence_length:i])
        
        # Price prediction target (next close price)
        if i < len(df_clean) - 1:
            y_price.append(df_clean[target_col].iloc[i+1])
        else:
            y_price.append(df_clean[target_col].iloc[i])
        
        # Signal classification target
        signal = df_clean['Signal'].iloc[i]
        # Convert to classification: -1->0, 0->1, 1->2
        y_signal.append(signal + 1)
    
    return np.array(X), np.array(y_price), np.array(y_signal), scaler, feature_cols

def create_pattern_pairs(sequences, labels, num_pairs=10000):
    """
    Create pairs for Siamese network training
    """
    pairs = []
    pair_labels = []
    
    # Flatten sequences for similarity comparison
    flattened_sequences = sequences.reshape(len(sequences), -1)
    
    for _ in range(num_pairs):
        # Random pair selection
        idx1, idx2 = np.random.choice(len(sequences), 2, replace=False)
        
        pairs.append([flattened_sequences[idx1], flattened_sequences[idx2]])
        
        # Label: 1 if same signal, 0 if different
        pair_labels.append(1 if labels[idx1] == labels[idx2] else 0)
    
    return np.array(pairs), np.array(pair_labels)

# Prepare training data for all timeframes
print("📊 Preparing training data for all timeframes...")

training_data = {}
scalers = {}

for tf, df in labeled_data.items():
    print(f"\nPreparing {tf} data...")
    
    # Prepare sequences
    X, y_price, y_signal, scaler, feature_cols = prepare_sequences(df, sequence_length=60)
    
    # Split data
    split_idx = int(0.8 * len(X))
    
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_price_train, y_price_test = y_price[:split_idx], y_price[split_idx:]
    y_signal_train, y_signal_test = y_signal[:split_idx], y_signal[split_idx:]
    
    # Create pattern pairs for Siamese network
    pairs_train, pair_labels_train = create_pattern_pairs(X_train, y_signal_train, num_pairs=5000)
    pairs_test, pair_labels_test = create_pattern_pairs(X_test, y_signal_test, num_pairs=1000)
    
    training_data[tf] = {
        'X_train': X_train, 'X_test': X_test,
        'y_price_train': y_price_train, 'y_price_test': y_price_test,
        'y_signal_train': y_signal_train, 'y_signal_test': y_signal_test,
        'pairs_train': pairs_train, 'pairs_test': pairs_test,
        'pair_labels_train': pair_labels_train, 'pair_labels_test': pair_labels_test,
        'feature_cols': feature_cols
    }
    
    scalers[tf] = scaler
    
    print(f"  ✅ {tf}: {len(X_train)} train, {len(X_test)} test sequences")
    print(f"     Features: {len(feature_cols)}, Sequence length: {X_train.shape[1]}")
    print(f"     Signal distribution: {np.bincount(y_signal_train)}")

print("\n📊 Data preparation completed!")

# Enhanced Training Progress Tracking
class TrainingProgressTracker:
    def __init__(self, model_name, total_epochs):
        self.model_name = model_name
        self.total_epochs = total_epochs
        self.start_time = time.time()
        
        # Create progress widgets
        self.epoch_progress = widgets.IntProgress(
            value=0,
            min=0,
            max=total_epochs,
            description='Epochs:',
            bar_style='info',
            style={'bar_color': '#1f77b4'},
            layout=widgets.Layout(width='100%')
        )
        
        self.batch_progress = widgets.IntProgress(
            value=0,
            min=0,
            max=100,
            description='Batches:',
            bar_style='success',
            style={'bar_color': '#2ca02c'},
            layout=widgets.Layout(width='100%')
        )
        
        self.status_text = widgets.HTML(
            value=f"<h3>🧠 Training {model_name}</h3>",
            layout=widgets.Layout(width='100%')
        )
        
        self.metrics_text = widgets.HTML(
            value="<p>Initializing training...</p>",
            layout=widgets.Layout(width='100%')
        )
        
        self.time_text = widgets.HTML(
            value="<p>⏱️ Time: 00:00:00</p>",
            layout=widgets.Layout(width='100%')
        )
        
        # Create layout
        self.progress_box = widgets.VBox([
            self.status_text,
            self.epoch_progress,
            self.batch_progress,
            self.metrics_text,
            self.time_text
        ])
        
        # Display the widget
        display(self.progress_box)
        
        # Store metrics for plotting
        self.train_losses = []
        self.test_losses = []
        self.train_accuracies = []
        self.test_accuracies = []
        
    def update_epoch(self, epoch, train_loss, test_loss, train_acc=None, test_acc=None):
        """Update progress for completed epoch"""
        self.epoch_progress.value = epoch + 1
        
        # Store metrics
        self.train_losses.append(train_loss)
        self.test_losses.append(test_loss)
        if train_acc is not None:
            self.train_accuracies.append(train_acc)
        if test_acc is not None:
            self.test_accuracies.append(test_acc)
        
        # Update time
        elapsed_time = time.time() - self.start_time
        hours, remainder = divmod(elapsed_time, 3600)
        minutes, seconds = divmod(remainder, 60)
        time_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
        
        # Estimate remaining time
        if epoch > 0:
            avg_time_per_epoch = elapsed_time / (epoch + 1)
            remaining_epochs = self.total_epochs - (epoch + 1)
            remaining_time = avg_time_per_epoch * remaining_epochs
            rem_hours, rem_remainder = divmod(remaining_time, 3600)
            rem_minutes, rem_seconds = divmod(rem_remainder, 60)
            remaining_str = f"{int(rem_hours):02d}:{int(rem_minutes):02d}:{int(rem_seconds):02d}"
        else:
            remaining_str = "--:--:--"
        
        self.time_text.value = f"<p>⏱️ Elapsed: {time_str} | 🔮 Remaining: {remaining_str}</p>"
        
        # Update metrics display
        if train_acc is not None and test_acc is not None:
            metrics_html = f"""
            <div style='background-color: #f0f0f0; padding: 10px; border-radius: 5px; margin: 5px 0;'>
                <p><strong>📊 Epoch {epoch + 1}/{self.total_epochs}</strong></p>
                <p>🔻 Train Loss: <span style='color: #d62728;'>{train_loss:.6f}</span> | 
                   🔻 Test Loss: <span style='color: #ff7f0e;'>{test_loss:.6f}</span></p>
                <p>✅ Train Acc: <span style='color: #2ca02c;'>{train_acc:.4f}</span> | 
                   ✅ Test Acc: <span style='color: #1f77b4;'>{test_acc:.4f}</span></p>
            </div>
            """
        else:
            metrics_html = f"""
            <div style='background-color: #f0f0f0; padding: 10px; border-radius: 5px; margin: 5px 0;'>
                <p><strong>📊 Epoch {epoch + 1}/{self.total_epochs}</strong></p>
                <p>🔻 Train Loss: <span style='color: #d62728;'>{train_loss:.6f}</span> | 
                   🔻 Test Loss: <span style='color: #ff7f0e;'>{test_loss:.6f}</span></p>
            </div>
            """
        
        self.metrics_text.value = metrics_html
        
        # Update progress bar color based on performance
        if len(self.test_losses) > 1:
            if self.test_losses[-1] < self.test_losses[-2]:
                self.epoch_progress.bar_style = 'success'
            else:
                self.epoch_progress.bar_style = 'warning'
    
    def update_batch(self, batch_idx, total_batches, current_loss):
        """Update progress within epoch"""
        progress_pct = int((batch_idx / total_batches) * 100)
        self.batch_progress.value = progress_pct
        self.batch_progress.description = f'Batch {batch_idx}/{total_batches} (Loss: {current_loss:.4f})'
    
    def complete_training(self):
        """Mark training as complete"""
        self.epoch_progress.bar_style = 'success'
        self.batch_progress.bar_style = 'success'
        self.batch_progress.value = 100
        
        total_time = time.time() - self.start_time
        hours, remainder = divmod(total_time, 3600)
        minutes, seconds = divmod(remainder, 60)
        time_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
        
        self.status_text.value = f"<h3>✅ {self.model_name} Training Complete!</h3>"
        self.time_text.value = f"<p>🎉 Total Training Time: {time_str}</p>"
        
        # Show final performance
        if self.train_accuracies:
            final_metrics = f"""
            <div style='background-color: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>
                <h4>🏆 Final Performance</h4>
                <p>🔻 Final Test Loss: <strong>{self.test_losses[-1]:.6f}</strong></p>
                <p>✅ Final Test Accuracy: <strong>{self.test_accuracies[-1]:.4f}</strong></p>
                <p>📈 Best Test Accuracy: <strong>{max(self.test_accuracies):.4f}</strong></p>
            </div>
            """
        else:
            final_metrics = f"""
            <div style='background-color: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>
                <h4>🏆 Final Performance</h4>
                <p>🔻 Final Test Loss: <strong>{self.test_losses[-1]:.6f}</strong></p>
                <p>📉 Best Test Loss: <strong>{min(self.test_losses):.6f}</strong></p>
            </div>
            """
        
        self.metrics_text.value = final_metrics
    
    def plot_training_curves(self):
        """Plot training curves in real-time"""
        if len(self.train_losses) < 2:
            return
        
        fig, axes = plt.subplots(1, 2 if self.train_accuracies else 1, figsize=(15, 5))
        if not self.train_accuracies:
            axes = [axes]
        
        # Plot losses
        epochs = range(1, len(self.train_losses) + 1)
        axes[0].plot(epochs, self.train_losses, 'b-', label='Train Loss', linewidth=2)
        axes[0].plot(epochs, self.test_losses, 'r-', label='Test Loss', linewidth=2)
        axes[0].set_title(f'{self.model_name} - Training Loss')
        axes[0].set_xlabel('Epoch')
        axes[0].set_ylabel('Loss')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # Plot accuracies if available
        if self.train_accuracies and len(axes) > 1:
            axes[1].plot(epochs, self.train_accuracies, 'g-', label='Train Accuracy', linewidth=2)
            axes[1].plot(epochs, self.test_accuracies, 'orange', label='Test Accuracy', linewidth=2)
            axes[1].set_title(f'{self.model_name} - Training Accuracy')
            axes[1].set_xlabel('Epoch')
            axes[1].set_ylabel('Accuracy')
            axes[1].legend()
            axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()

# Live plotting function
def create_live_plot():
    """Create live updating plot for training metrics"""
    fig, ax = plt.subplots(figsize=(12, 6))
    return fig, ax

def update_live_plot(fig, ax, train_losses, test_losses, model_name, train_acc=None, test_acc=None):
    """Update live plot with new data"""
    ax.clear()
    
    epochs = range(1, len(train_losses) + 1)
    
    # Plot losses
    ax.plot(epochs, train_losses, 'b-', label='Train Loss', linewidth=2, marker='o')
    ax.plot(epochs, test_losses, 'r-', label='Test Loss', linewidth=2, marker='s')
    
    ax.set_title(f'{model_name} - Training Progress (Real-time)', fontsize=14, fontweight='bold')
    ax.set_xlabel('Epoch', fontsize=12)
    ax.set_ylabel('Loss', fontsize=12)
    ax.legend(fontsize=10)
    ax.grid(True, alpha=0.3)
    
    # Add current values as text
    if train_losses and test_losses:
        current_info = f'Current - Train: {train_losses[-1]:.4f}, Test: {test_losses[-1]:.4f}'
        if train_acc and test_acc:
            current_info += f'\nAccuracy - Train: {train_acc[-1]:.3f}, Test: {test_acc[-1]:.3f}'
        
        ax.text(0.02, 0.98, current_info, transform=ax.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    display(fig)
    clear_output(wait=True)

print("📊 Training progress tracking system ready!")

def train_temporal_fusion_transformer(X_train, y_train, X_test, y_test, epochs=50):
    """
    Train Temporal Fusion Transformer for price prediction
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Training TFT on {device}")
    
    # Model initialization
    input_size = X_train.shape[2]
    model = TemporalFusionTransformer(input_size=input_size).to(device)
    
    # Loss and optimizer
    criterion = nn.MSELoss()
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
    
    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train).to(device)
    y_train_tensor = torch.FloatTensor(y_train).unsqueeze(1).to(device)
    X_test_tensor = torch.FloatTensor(X_test).to(device)
    y_test_tensor = torch.FloatTensor(y_test).unsqueeze(1).to(device)
    
    # Training loop
    train_losses = []
    test_losses = []
    
    batch_size = 32
    
    for epoch in range(epochs):
        model.train()
        epoch_train_loss = 0
        
        # Mini-batch training
        for i in range(0, len(X_train_tensor), batch_size):
            batch_X = X_train_tensor[i:i+batch_size]
            batch_y = y_train_tensor[i:i+batch_size]
            
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            
            epoch_train_loss += loss.item()
        
        # Validation
        model.eval()
        with torch.no_grad():
            test_outputs = model(X_test_tensor)
            test_loss = criterion(test_outputs, y_test_tensor)
        
        avg_train_loss = epoch_train_loss / (len(X_train_tensor) // batch_size)
        train_losses.append(avg_train_loss)
        test_losses.append(test_loss.item())
        
        scheduler.step(test_loss)
        
        if epoch % 10 == 0:
            print(f"Epoch {epoch}: Train Loss: {avg_train_loss:.6f}, Test Loss: {test_loss.item():.6f}")
    
    return model, train_losses, test_losses

def train_cnn_bilstm_attention(X_train, y_train, X_test, y_test, epochs=50):
    """
    Train CNN + BiLSTM + Attention for signal generation
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Training CNN+BiLSTM+Attention on {device}")
    
    # Model initialization
    input_size = X_train.shape[2]
    model = CNNBiLSTMAttention(input_size=input_size, num_classes=3).to(device)
    
    # Loss and optimizer
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
    
    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train).to(device)
    y_train_tensor = torch.LongTensor(y_train).to(device)
    X_test_tensor = torch.FloatTensor(X_test).to(device)
    y_test_tensor = torch.LongTensor(y_test).to(device)
    
    # Training loop
    train_losses = []
    test_losses = []
    train_accuracies = []
    test_accuracies = []
    
    batch_size = 32
    
    for epoch in range(epochs):
        model.train()
        epoch_train_loss = 0
        correct_train = 0
        total_train = 0
        
        # Mini-batch training
        for i in range(0, len(X_train_tensor), batch_size):
            batch_X = X_train_tensor[i:i+batch_size]
            batch_y = y_train_tensor[i:i+batch_size]
            
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            
            epoch_train_loss += loss.item()
            
            # Calculate accuracy
            _, predicted = torch.max(outputs.data, 1)
            total_train += batch_y.size(0)
            correct_train += (predicted == batch_y).sum().item()
        
        # Validation
        model.eval()
        with torch.no_grad():
            test_outputs = model(X_test_tensor)
            test_loss = criterion(test_outputs, y_test_tensor)
            
            _, predicted_test = torch.max(test_outputs.data, 1)
            test_accuracy = (predicted_test == y_test_tensor).sum().item() / len(y_test_tensor)
        
        avg_train_loss = epoch_train_loss / (len(X_train_tensor) // batch_size)
        train_accuracy = correct_train / total_train
        
        train_losses.append(avg_train_loss)
        test_losses.append(test_loss.item())
        train_accuracies.append(train_accuracy)
        test_accuracies.append(test_accuracy)
        
        scheduler.step(test_loss)
        
        if epoch % 10 == 0:
            print(f"Epoch {epoch}: Train Loss: {avg_train_loss:.4f}, Test Loss: {test_loss.item():.4f}")
            print(f"         Train Acc: {train_accuracy:.4f}, Test Acc: {test_accuracy:.4f}")
    
    return model, train_losses, test_losses, train_accuracies, test_accuracies

print("🚀 Training functions defined!")

# Enhanced training functions with progress tracking
def train_tft_with_progress(X_train, y_train, X_test, y_test, epochs=50, timeframe=''):
    """
    Train Temporal Fusion Transformer with enhanced progress tracking
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model_name = f'TFT ({timeframe})' if timeframe else 'Temporal Fusion Transformer'
    
    # Initialize progress tracker
    progress_tracker = TrainingProgressTracker(model_name, epochs)
    
    # Model initialization
    input_size = X_train.shape[2]
    model = TemporalFusionTransformer(input_size=input_size).to(device)
    
    # Loss and optimizer
    criterion = nn.MSELoss()
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
    
    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train).to(device)
    y_train_tensor = torch.FloatTensor(y_train).unsqueeze(1).to(device)
    X_test_tensor = torch.FloatTensor(X_test).to(device)
    y_test_tensor = torch.FloatTensor(y_test).unsqueeze(1).to(device)
    
    # Training loop
    train_losses = []
    test_losses = []
    batch_size = 32
    total_batches = len(X_train_tensor) // batch_size
    
    for epoch in range(epochs):
        model.train()
        epoch_train_loss = 0
        
        # Mini-batch training with progress tracking
        for i, batch_start in enumerate(range(0, len(X_train_tensor), batch_size)):
            batch_X = X_train_tensor[batch_start:batch_start+batch_size]
            batch_y = y_train_tensor[batch_start:batch_start+batch_size]
            
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            
            batch_loss = loss.item()
            epoch_train_loss += batch_loss
            
            # Update batch progress every 10 batches
            if i % 10 == 0 or i == total_batches - 1:
                progress_tracker.update_batch(i + 1, total_batches, batch_loss)
        
        # Validation
        model.eval()
        with torch.no_grad():
            test_outputs = model(X_test_tensor)
            test_loss = criterion(test_outputs, y_test_tensor)
        
        avg_train_loss = epoch_train_loss / total_batches
        train_losses.append(avg_train_loss)
        test_losses.append(test_loss.item())
        
        # Update progress tracker
        progress_tracker.update_epoch(epoch, avg_train_loss, test_loss.item())
        
        scheduler.step(test_loss)
    
    # Complete training
    progress_tracker.complete_training()
    progress_tracker.plot_training_curves()
    
    return model, train_losses, test_losses

def train_signal_model_with_progress(X_train, y_train, X_test, y_test, epochs=50, timeframe=''):
    """
    Train CNN + BiLSTM + Attention with enhanced progress tracking
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model_name = f'Signal Model ({timeframe})' if timeframe else 'CNN+BiLSTM+Attention'
    
    # Initialize progress tracker
    progress_tracker = TrainingProgressTracker(model_name, epochs)
    
    # Model initialization
    input_size = X_train.shape[2]
    model = CNNBiLSTMAttention(input_size=input_size, num_classes=3).to(device)
    
    # Loss and optimizer
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
    
    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train).to(device)
    y_train_tensor = torch.LongTensor(y_train).to(device)
    X_test_tensor = torch.FloatTensor(X_test).to(device)
    y_test_tensor = torch.LongTensor(y_test).to(device)
    
    # Training loop
    train_losses = []
    test_losses = []
    train_accuracies = []
    test_accuracies = []
    
    batch_size = 32
    total_batches = len(X_train_tensor) // batch_size
    
    for epoch in range(epochs):
        model.train()
        epoch_train_loss = 0
        correct_train = 0
        total_train = 0
        
        # Mini-batch training with progress tracking
        for i, batch_start in enumerate(range(0, len(X_train_tensor), batch_size)):
            batch_X = X_train_tensor[batch_start:batch_start+batch_size]
            batch_y = y_train_tensor[batch_start:batch_start+batch_size]
            
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            
            batch_loss = loss.item()
            epoch_train_loss += batch_loss
            
            # Calculate accuracy
            _, predicted = torch.max(outputs.data, 1)
            total_train += batch_y.size(0)
            correct_train += (predicted == batch_y).sum().item()
            
            # Update batch progress every 10 batches
            if i % 10 == 0 or i == total_batches - 1:
                current_acc = correct_train / total_train if total_train > 0 else 0
                progress_tracker.update_batch(i + 1, total_batches, batch_loss)
        
        # Validation
        model.eval()
        with torch.no_grad():
            test_outputs = model(X_test_tensor)
            test_loss = criterion(test_outputs, y_test_tensor)
            
            _, predicted_test = torch.max(test_outputs.data, 1)
            test_accuracy = (predicted_test == y_test_tensor).sum().item() / len(y_test_tensor)
        
        avg_train_loss = epoch_train_loss / total_batches
        train_accuracy = correct_train / total_train
        
        train_losses.append(avg_train_loss)
        test_losses.append(test_loss.item())
        train_accuracies.append(train_accuracy)
        test_accuracies.append(test_accuracy)
        
        # Update progress tracker
        progress_tracker.update_epoch(epoch, avg_train_loss, test_loss.item(), train_accuracy, test_accuracy)
        
        scheduler.step(test_loss)
    
    # Complete training
    progress_tracker.complete_training()
    progress_tracker.plot_training_curves()
    
    return model, train_losses, test_losses, train_accuracies, test_accuracies

def train_siamese_with_progress(pairs_train, pair_labels_train, epochs=20, timeframe=''):
    """
    Train Siamese Network with enhanced progress tracking
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model_name = f'Siamese ({timeframe})' if timeframe else 'Siamese Network'
    
    # Initialize progress tracker
    progress_tracker = TrainingProgressTracker(model_name, epochs)
    
    # Model initialization
    input_size = pairs_train.shape[2]  # Flattened sequence size
    model = SiameseNetwork(input_size=input_size).to(device)
    
    # Loss and optimizer
    criterion = nn.BCELoss()
    optimizer = optim.AdamW(model.parameters(), lr=0.001)
    
    # Convert to tensors
    pairs_train_tensor = torch.FloatTensor(pairs_train).to(device)
    pair_labels_train_tensor = torch.FloatTensor(pair_labels_train).unsqueeze(1).to(device)
    
    siamese_losses = []
    batch_size = 32
    total_batches = len(pairs_train_tensor) // batch_size
    
    for epoch in range(epochs):
        model.train()
        epoch_loss = 0
        
        for i, batch_start in enumerate(range(0, len(pairs_train_tensor), batch_size)):
            batch_pairs = pairs_train_tensor[batch_start:batch_start+batch_size]
            batch_labels = pair_labels_train_tensor[batch_start:batch_start+batch_size]
            
            optimizer.zero_grad()
            
            # Forward pass
            similarity, _, _ = model(batch_pairs[:, 0], batch_pairs[:, 1])
            loss = criterion(similarity, batch_labels)
            
            loss.backward()
            optimizer.step()
            
            batch_loss = loss.item()
            epoch_loss += batch_loss
            
            # Update batch progress every 10 batches
            if i % 10 == 0 or i == total_batches - 1:
                progress_tracker.update_batch(i + 1, total_batches, batch_loss)
        
        avg_loss = epoch_loss / total_batches
        siamese_losses.append(avg_loss)
        
        # Update progress tracker (using loss as both train and test for simplicity)
        progress_tracker.update_epoch(epoch, avg_loss, avg_loss)
    
    # Complete training
    progress_tracker.complete_training()
    progress_tracker.plot_training_curves()
    
    return model, siamese_losses

print("🎯 Enhanced training functions with progress tracking ready!")

# Train models for primary timeframes (M15, H1, H4) with enhanced progress tracking
primary_timeframes = ['M15', 'H1', 'H4']
trained_models = {}

print("🎯 Starting enhanced training for all models with progress tracking...\n")
print("📊 Features:")
print("   ✅ Real-time progress bars")
print("   ✅ Live training metrics")
print("   ✅ Time estimation")
print("   ✅ Interactive widgets")
print("   ✅ Training curve visualization\n")

for tf in primary_timeframes:
    if tf not in training_data:
        print(f"⚠️ Skipping {tf} - no training data available")
        continue
    
    print(f"\n{'='*60}")
    print(f"🚀 Training Neural G1 models for {tf} timeframe")
    print(f"{'='*60}")
    
    data = training_data[tf]
    
    # 1. Train Temporal Fusion Transformer (Price Prediction) with Progress
    print(f"\n📈 Starting TFT training for {tf}...")
    tft_model, tft_train_losses, tft_test_losses = train_tft_with_progress(
        data['X_train'], data['y_price_train'],
        data['X_test'], data['y_price_test'],
        epochs=30, timeframe=tf
    )
    
    print(f"\n" + "="*40)
    
    # 2. Train CNN + BiLSTM + Attention (Signal Generation) with Progress
    print(f"\n🧠 Starting Signal Model training for {tf}...")
    signal_model, signal_train_losses, signal_test_losses, signal_train_acc, signal_test_acc = train_signal_model_with_progress(
        data['X_train'], data['y_signal_train'],
        data['X_test'], data['y_signal_test'],
        epochs=30, timeframe=tf
    )
    
    print(f"\n" + "="*40)
    
    # 3. Train Siamese Network (Pattern Similarity) with Progress
    print(f"\n🔗 Starting Siamese Network training for {tf}...")
    siamese_model, siamese_losses = train_siamese_with_progress(
        data['pairs_train'], data['pair_labels_train'],
        epochs=20, timeframe=tf
    )
    
    # Store trained models
    trained_models[tf] = {
        'tft_model': tft_model,
        'signal_model': signal_model,
        'siamese_model': siamese_model,
        'tft_losses': {'train': tft_train_losses, 'test': tft_test_losses},
        'signal_losses': {'train': signal_train_losses, 'test': signal_test_losses},
        'signal_accuracies': {'train': signal_train_acc, 'test': signal_test_acc},
        'siamese_losses': siamese_losses,
        'scaler': scalers[tf],
        'feature_cols': data['feature_cols']
    }
    
    # Display final performance summary
    print(f"\n" + "="*60)
    print(f"🏆 {tf} TRAINING COMPLETED - FINAL RESULTS")
    print(f"="*60)
    print(f"📈 TFT Final Test Loss: {tft_test_losses[-1]:.6f}")
    print(f"🎯 Signal Final Test Accuracy: {signal_test_acc[-1]:.4f} ({signal_test_acc[-1]*100:.2f}%)")
    print(f"🔗 Siamese Final Loss: {siamese_losses[-1]:.4f}")
    print(f"⏱️ Timeframe: {tf} | Models: 3/3 Complete")
    print(f"="*60)

print("\n" + "🎉"*20)
print("🎉 ALL NEURAL G1 MODEL TRAINING COMPLETED! 🎉")
print("🎉"*20)
print(f"\n📊 Training Summary:")
print(f"   ✅ Timeframes trained: {len(trained_models)}")
print(f"   ✅ Total models: {len(trained_models) * 3}")
print(f"   ✅ Model types: TFT, Signal Generator, Siamese Network")
print(f"   ✅ All models ready for deployment!")

# Evaluation and visualization
def plot_training_history(losses_dict, title):
    """Plot training history"""
    plt.figure(figsize=(12, 4))
    
    for tf, losses in losses_dict.items():
        if 'train' in losses and 'test' in losses:
            plt.subplot(1, 2, 1)
            plt.plot(losses['train'], label=f'{tf} Train')
            plt.plot(losses['test'], label=f'{tf} Test')
            plt.title(f'{title} - Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()
            plt.grid(True)
    
    plt.tight_layout()
    plt.show()

def evaluate_signal_model(model, X_test, y_test, timeframe):
    """Evaluate signal generation model"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.eval()
    
    with torch.no_grad():
        X_test_tensor = torch.FloatTensor(X_test).to(device)
        outputs = model(X_test_tensor)
        _, predicted = torch.max(outputs, 1)
        predicted = predicted.cpu().numpy()
    
    # Classification report
    print(f"\n📊 {timeframe} Signal Model Evaluation:")
    print(classification_report(y_test, predicted, target_names=['Sell', 'Hold', 'Buy']))
    
    # Confusion matrix
    cm = confusion_matrix(y_test, predicted)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=['Sell', 'Hold', 'Buy'],
                yticklabels=['Sell', 'Hold', 'Buy'])
    plt.title(f'{timeframe} - Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.show()
    
    return predicted

# Evaluate all trained models
print("📊 Evaluating trained models...\n")

tft_losses = {}
signal_losses = {}

for tf, models in trained_models.items():
    print(f"\n{'='*40}")
    print(f"📊 Evaluating {tf} models")
    print(f"{'='*40}")
    
    # Collect losses for plotting
    tft_losses[tf] = models['tft_losses']
    signal_losses[tf] = models['signal_losses']
    
    # Evaluate signal model
    test_data = training_data[tf]
    predictions = evaluate_signal_model(
        models['signal_model'],
        test_data['X_test'],
        test_data['y_signal_test'],
        tf
    )

# Plot training histories
print("\n📈 Plotting training histories...")
plot_training_history(tft_losses, "Temporal Fusion Transformer")
plot_training_history(signal_losses, "Signal Generation Model")

print("\n✅ Model evaluation completed!")

# Save all trained models
import os
from datetime import datetime

# Create save directory
save_dir = '/content/drive/MyDrive/Neural_G1/trained_models'
os.makedirs(save_dir, exist_ok=True)

timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

print(f"💾 Saving trained models to {save_dir}...\n")

for tf, models in trained_models.items():
    tf_dir = os.path.join(save_dir, f'{tf}_{timestamp}')
    os.makedirs(tf_dir, exist_ok=True)
    
    # Save PyTorch models
    torch.save(models['tft_model'].state_dict(), 
               os.path.join(tf_dir, 'tft_model.pth'))
    torch.save(models['signal_model'].state_dict(), 
               os.path.join(tf_dir, 'signal_model.pth'))
    torch.save(models['siamese_model'].state_dict(), 
               os.path.join(tf_dir, 'siamese_model.pth'))
    
    # Save scaler
    with open(os.path.join(tf_dir, 'scaler.pkl'), 'wb') as f:
        pickle.dump(models['scaler'], f)
    
    # Save feature columns
    with open(os.path.join(tf_dir, 'feature_cols.json'), 'w') as f:
        json.dump(models['feature_cols'], f)
    
    # Save training history
    training_history = {
        'tft_losses': models['tft_losses'],
        'signal_losses': models['signal_losses'],
        'signal_accuracies': models['signal_accuracies'],
        'siamese_losses': models['siamese_losses']
    }
    
    with open(os.path.join(tf_dir, 'training_history.json'), 'w') as f:
        json.dump(training_history, f)
    
    # Save model architecture info
    model_info = {
        'timeframe': tf,
        'timestamp': timestamp,
        'input_size': len(models['feature_cols']),
        'sequence_length': 60,
        'num_classes': 3,
        'models': {
            'tft': 'TemporalFusionTransformer',
            'signal': 'CNNBiLSTMAttention',
            'siamese': 'SiameseNetwork'
        }
    }
    
    with open(os.path.join(tf_dir, 'model_info.json'), 'w') as f:
        json.dump(model_info, f, indent=2)
    
    print(f"✅ {tf} models saved to {tf_dir}")

# Create a summary file
summary = {
    'training_date': timestamp,
    'timeframes_trained': list(trained_models.keys()),
    'models_per_timeframe': ['TFT', 'CNN+BiLSTM+Attention', 'SiameseNetwork'],
    'total_models': len(trained_models) * 3,
    'save_directory': save_dir,
    'performance_summary': {}
}

for tf, models in trained_models.items():
    summary['performance_summary'][tf] = {
        'tft_final_test_loss': float(models['tft_losses']['test'][-1]),
        'signal_final_test_accuracy': float(models['signal_accuracies']['test'][-1]),
        'siamese_final_loss': float(models['siamese_losses'][-1])
    }

with open(os.path.join(save_dir, f'training_summary_{timestamp}.json'), 'w') as f:
    json.dump(summary, f, indent=2)

print(f"\n🎉 All models saved successfully!")
print(f"📁 Save directory: {save_dir}")
print(f"📊 Training summary saved as: training_summary_{timestamp}.json")
print(f"\n📋 Summary:")
print(f"   - Timeframes trained: {', '.join(trained_models.keys())}")
print(f"   - Total models: {len(trained_models) * 3}")
print(f"   - Models per timeframe: TFT, CNN+BiLSTM+Attention, SiameseNetwork")

# Display final performance summary
print(f"\n📊 Final Performance Summary:")
for tf, perf in summary['performance_summary'].items():
    print(f"   {tf}:")
    print(f"     - TFT Test Loss: {perf['tft_final_test_loss']:.6f}")
    print(f"     - Signal Test Accuracy: {perf['signal_final_test_accuracy']:.4f}")
    print(f"     - Siamese Loss: {perf['siamese_final_loss']:.4f}")

print("\n🚀 Neural G1 training pipeline completed successfully!")
print("\n📝 Next steps:")
print("   1. Deploy models to production environment")
print("   2. Set up real-time data pipeline")
print("   3. Implement Telegram notification system")
print("   4. Create web dashboard for monitoring")
print("   5. Enable online learning pipeline")