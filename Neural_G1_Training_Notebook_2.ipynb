{"cells": [{"cell_type": "markdown", "metadata": {"id": "neural_g1_title"}, "source": ["# 🧠 Neural G1 - Complete AI Training Pipeline\n", "## Self-Learning AI for XAUUSD Trading\n", "\n", "**Author**: Neural G1 Development Team  \n", "**Date**: 2025-06-14  \n", "**Purpose**: Train all AI models for the Neural G1 trading system\n", "\n", "### 📋 Models to Train:\n", "1. **Temporal Fusion Transformer (TFT)** - Price Prediction\n", "2. **CNN + BiLSTM + Attention** - Signal Generation\n", "3. **Vision Transformer (ViT)** - Chart Pattern Recognition\n", "4. **Siamese Networks** - Pattern Similarity Matching\n", "5. **Confidence Synthesizer** - Final Decision Engine\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "setup_section"}, "source": ["## 🔧 Environment Setup & Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_dependencies"}, "outputs": [], "source": ["# Install required packages\n", "!pip install tensorflow==2.13.0\n", "!pip install torch torchvision torchaudio\n", "!pip install transformers\n", "!pip install ta-lib\n", "!pip install pandas numpy mat<PERSON><PERSON><PERSON>b seaborn\n", "!pip install scikit-learn\n", "!pip install plotly\n", "!pip install yfinance\n", "!pip install pytorch-forecasting\n", "!pip install lightning\n", "!pip install optuna\n", "!pip install wandb\n", "!pip install tqdm\n", "!pip install ipywidgets\n", "!pip install matplotlib\n", "\n", "# Enable widget extensions for Colab\n", "from google.colab import output\n", "output.enable_custom_widget_manager()\n", "\n", "# For Google Drive integration\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "print(\"✅ All dependencies installed successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "imports"}, "outputs": [], "source": ["# Core imports\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Deep Learning\n", "import tensorflow as tf\n", "from tensorflow import keras\n", "from tensorflow.keras import layers, Model, callbacks\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import DataLoader, Dataset\n", "\n", "# Technical Analysis\n", "import talib\n", "from sklearn.preprocessing import MinMaxScaler, StandardScaler\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "\n", "# Visualization\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "from IPython.display import display, clear_output, HTML\n", "import ipywidgets as widgets\n", "\n", "# Progress tracking\n", "from tqdm.auto import tqdm\n", "import time\n", "\n", "# Utilities\n", "import os\n", "import json\n", "import pickle\n", "from datetime import datetime, timedelta\n", "import random\n", "\n", "# Set random seeds for reproducibility\n", "np.random.seed(42)\n", "tf.random.set_seed(42)\n", "torch.manual_seed(42)\n", "\n", "print(\"📦 All imports completed successfully!\")\n", "print(f\"🔥 TensorFlow version: {tf.__version__}\")\n", "print(f\"🔥 PyTorch version: {torch.__version__}\")\n", "print(f\"🖥️ GPU Available: {tf.config.list_physical_devices('GPU')}\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_section"}, "source": ["## 📊 Data Loading & Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "data_loading"}, "outputs": [], "source": ["# Data loading function for multiple timeframes\n", "def load_forex_data(file_path, timeframe):\n", "    \"\"\"\n", "    Load and preprocess forex data from CSV files\n", "    Expected format: Date, Time, Open, High, Low, Close, Volume\n", "    \"\"\"\n", "    try:\n", "        # Try different separators\n", "        for sep in ['\\t', ',', ';']:\n", "            try:\n", "                df = pd.read_csv(file_path, sep=sep)\n", "                if len(df.columns) >= 6:\n", "                    break\n", "            except:\n", "                continue\n", "        \n", "        # Standardize column names\n", "        expected_cols = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume']\n", "        if len(df.columns) >= 6:\n", "            df.columns = expected_cols[:len(df.columns)]\n", "        \n", "        # Combine Date and Time if separate\n", "        if 'Time' in df.columns:\n", "            df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])\n", "        else:\n", "            df['DateTime'] = pd.to_datetime(df['Date'])\n", "        \n", "        # Set DateTime as index\n", "        df.set_index('DateTime', inplace=True)\n", "        \n", "        # Keep only OHLCV columns\n", "        ohlcv_cols = ['Open', 'High', 'Low', 'Close', 'Volume']\n", "        df = df[ohlcv_cols]\n", "        \n", "        # Convert to numeric\n", "        for col in ohlcv_cols:\n", "            df[col] = pd.to_numeric(df[col], errors='coerce')\n", "        \n", "        # Remove any rows with NaN values\n", "        df.dropna(inplace=True)\n", "        \n", "        # Add timeframe identifier\n", "        df['Timeframe'] = timeframe\n", "        \n", "        print(f\"✅ Loaded {timeframe} data: {len(df)} rows, {df.index[0]} to {df.index[-1]}\")\n", "        return df\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error loading {file_path}: {str(e)}\")\n", "        return None\n", "\n", "# Sample data generation (if CSV files not available)\n", "def generate_sample_data(timeframe, days=365):\n", "    \"\"\"\n", "    Generate realistic XAUUSD sample data for testing\n", "    \"\"\"\n", "    # Timeframe to minutes mapping\n", "    tf_minutes = {\n", "        'M1': 1, 'M5': 5, 'M15': 15, 'M30': 30,\n", "        'H1': 60, 'H4': 240, 'D1': 1440\n", "    }\n", "    \n", "    minutes = tf_minutes.get(timeframe, 15)\n", "    total_periods = int((days * 24 * 60) / minutes)\n", "    \n", "    # Generate realistic price data\n", "    np.random.seed(42)\n", "    base_price = 2000.0  # XAUUSD base price\n", "    \n", "    # Generate price movements with trend and volatility\n", "    returns = np.random.normal(0, 0.001, total_periods)  # Small random returns\n", "    trend = np.sin(np.linspace(0, 4*np.pi, total_periods)) * 0.0005  # Cyclical trend\n", "    prices = base_price * np.exp(np.cumsum(returns + trend))\n", "    \n", "    # Generate OHLC from prices\n", "    data = []\n", "    for i in range(len(prices)):\n", "        if i == 0:\n", "            open_price = prices[i]\n", "        else:\n", "            open_price = data[-1]['Close']\n", "        \n", "        close_price = prices[i]\n", "        high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, 0.0005)))\n", "        low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, 0.0005)))\n", "        volume = np.random.randint(100, 1000)\n", "        \n", "        data.append({\n", "            'Open': open_price,\n", "            'High': high_price,\n", "            'Low': low_price,\n", "            'Close': close_price,\n", "            'Volume': volume\n", "        })\n", "    \n", "    # Create DataFrame with proper datetime index\n", "    start_date = datetime.now() - timed<PERSON>ta(days=days)\n", "    date_range = pd.date_range(start=start_date, periods=total_periods, freq=f'{minutes}min')\n", "    \n", "    df = pd.DataFrame(data, index=date_range)\n", "    df['Timeframe'] = timeframe\n", "    \n", "    print(f\"✅ Generated {timeframe} sample data: {len(df)} rows\")\n", "    return df\n", "\n", "# Load or generate data for all timeframes\n", "timeframes = ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1']\n", "forex_data = {}\n", "\n", "print(\"📊 Loading XAUUSD data for all timeframes...\")\n", "\n", "for tf in timeframes:\n", "    # Try to load from CSV first\n", "    csv_path = f'/content/drive/MyDrive/Neural_G1/data/XAUUSD_{tf}.csv'\n", "    \n", "    if os.path.exists(csv_path):\n", "        forex_data[tf] = load_forex_data(csv_path, tf)\n", "    else:\n", "        print(f\"⚠️ CSV file not found for {tf}, generating sample data...\")\n", "        forex_data[tf] = generate_sample_data(tf, days=180)  # 6 months of data\n", "\n", "print(\"\\n📈 Data loading summary:\")\n", "for tf, df in forex_data.items():\n", "    if df is not None:\n", "        print(f\"{tf}: {len(df):,} candles | {df.index[0].strftime('%Y-%m-%d')} to {df.index[-1].strftime('%Y-%m-%d')}\")"]}, {"cell_type": "markdown", "metadata": {"id": "technical_indicators"}, "source": ["## 📈 Technical Indicators & Feature Engineering"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "indicators_calculation"}, "outputs": [], "source": ["def calculate_technical_indicators(df):\n", "    \"\"\"\n", "    Calculate comprehensive technical indicators for trading signals\n", "    \"\"\"\n", "    df = df.copy()\n", "    \n", "    # Price-based indicators\n", "    df['SMA_10'] = talib.SMA(df['Close'], timeperiod=10)\n", "    df['SMA_20'] = talib.SMA(df['Close'], timeperiod=20)\n", "    df['EMA_5'] = talib.EMA(df['Close'], timeperiod=5)\n", "    df['EMA_10'] = talib.EMA(df['Close'], timeperiod=10)\n", "    df['EMA_20'] = talib.EMA(df['Close'], timeperiod=20)\n", "    df['EMA_50'] = talib.EMA(df['Close'], timeperiod=50)\n", "    \n", "    # Bollinger Bands\n", "    df['BB_Upper'], df['BB_Middle'], df['BB_Lower'] = talib.BBANDS(df['Close'], timeperiod=20)\n", "    df['BB_Width'] = (df['BB_Upper'] - df['BB_Lower']) / df['BB_Middle']\n", "    df['BB_Position'] = (df['Close'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])\n", "    \n", "    # RSI\n", "    df['RSI'] = talib.RSI(df['Close'], timeperiod=14)\n", "    df['RSI_Oversold'] = (df['RSI'] < 30).astype(int)\n", "    df['RSI_Overbought'] = (df['RSI'] > 70).astype(int)\n", "    \n", "    # MACD\n", "    df['MACD'], df['MACD_Signal'], df['MACD_Hist'] = talib.MACD(df['Close'])\n", "    df['MACD_Bullish'] = (df['MACD'] > df['MACD_Signal']).astype(int)\n", "    \n", "    # Stochastic\n", "    df['<PERSON><PERSON>_<PERSON>'], df['<PERSON>och_D'] = talib.STOCH(df['High'], df['Low'], df['Close'])\n", "    \n", "    # ATR for volatility\n", "    df['ATR'] = talib.ATR(df['High'], df['Low'], df['Close'], timeperiod=14)\n", "    \n", "    # Price patterns\n", "    df['Price_Change'] = df['Close'].pct_change()\n", "    df['High_Low_Ratio'] = df['High'] / df['Low']\n", "    df['Close_Open_Ratio'] = df['Close'] / df['Open']\n", "    \n", "    # Volume indicators\n", "    df['Volume_SMA'] = talib.SMA(df['Volume'], timeperiod=20)\n", "    df['Volume_Ratio'] = df['Volume'] / df['Volume_SMA']\n", "    \n", "    # Trend indicators\n", "    df['ADX'] = talib.ADX(df['High'], df['Low'], df['Close'], timeperiod=14)\n", "    df['CCI'] = talib.CCI(df['High'], df['Low'], df['Close'], timeperiod=14)\n", "    \n", "    return df\n", "\n", "def identify_supply_demand_zones(df, window=20, strength_threshold=0.5):\n", "    \"\"\"\n", "    Identify Supply and Demand zones - Primary framework for Neural G1\n", "    \"\"\"\n", "    df = df.copy()\n", "    \n", "    # Calculate pivot points\n", "    df['Pivot_High'] = df['High'].rolling(window=window, center=True).max() == df['High']\n", "    df['Pivot_Low'] = df['Low'].rolling(window=window, center=True).min() == df['Low']\n", "    \n", "    # Supply zones (resistance levels)\n", "    supply_zones = []\n", "    demand_zones = []\n", "    \n", "    for i in range(window, len(df) - window):\n", "        if df['Pivot_High'].iloc[i]:\n", "            # Check zone strength based on volume and price action\n", "            zone_high = df['High'].iloc[i]\n", "            zone_low = df['Low'].iloc[i]\n", "            zone_volume = df['Volume'].iloc[i]\n", "            \n", "            # Calculate zone strength\n", "            avg_volume = df['Volume'].iloc[i-window:i+window].mean()\n", "            volume_strength = zone_volume / avg_volume if avg_volume > 0 else 1\n", "            \n", "            if volume_strength > strength_threshold:\n", "                supply_zones.append({\n", "                    'index': i,\n", "                    'high': zone_high,\n", "                    'low': zone_low,\n", "                    'strength': volume_strength,\n", "                    'type': 'supply'\n", "                })\n", "        \n", "        if df['<PERSON>vot_Low'].iloc[i]:\n", "            # Demand zones (support levels)\n", "            zone_high = df['High'].iloc[i]\n", "            zone_low = df['Low'].iloc[i]\n", "            zone_volume = df['Volume'].iloc[i]\n", "            \n", "            avg_volume = df['Volume'].iloc[i-window:i+window].mean()\n", "            volume_strength = zone_volume / avg_volume if avg_volume > 0 else 1\n", "            \n", "            if volume_strength > strength_threshold:\n", "                demand_zones.append({\n", "                    'index': i,\n", "                    'high': zone_high,\n", "                    'low': zone_low,\n", "                    'strength': volume_strength,\n", "                    'type': 'demand'\n", "                })\n", "    \n", "    # Add zone proximity features\n", "    df['Near_Supply_Zone'] = 0.0\n", "    df['Near_Demand_Zone'] = 0.0\n", "    df['Zone_Strength'] = 0.0\n", "    \n", "    for zone in supply_zones:\n", "        mask = (df.index >= df.index[zone['index'] - window]) & (df.index <= df.index[zone['index'] + window])\n", "        df.loc[mask, 'Near_Supply_Zone'] = zone['strength']\n", "        df.loc[mask, 'Zone_Strength'] = zone['strength']\n", "    \n", "    for zone in demand_zones:\n", "        mask = (df.index >= df.index[zone['index'] - window]) & (df.index <= df.index[zone['index'] + window])\n", "        df.loc[mask, 'Near_Demand_Zone'] = zone['strength']\n", "        df.loc[mask, 'Zone_Strength'] = max(df.loc[mask, 'Zone_Strength'].max(), zone['strength'])\n", "    \n", "    return df, supply_zones, demand_zones\n", "\n", "# Apply technical indicators to all timeframes\n", "print(\"🔧 Calculating technical indicators for all timeframes...\")\n", "\n", "processed_data = {}\n", "supply_demand_zones = {}\n", "\n", "for tf, df in forex_data.items():\n", "    if df is not None:\n", "        print(f\"Processing {tf}...\")\n", "        \n", "        # Calculate technical indicators\n", "        df_with_indicators = calculate_technical_indicators(df)\n", "        \n", "        # Identify supply/demand zones\n", "        df_processed, supply_zones, demand_zones = identify_supply_demand_zones(df_with_indicators)\n", "        \n", "        processed_data[tf] = df_processed\n", "        supply_demand_zones[tf] = {\n", "            'supply': supply_zones,\n", "            'demand': demand_zones\n", "        }\n", "        \n", "        print(f\"  ✅ {tf}: {len(supply_zones)} supply zones, {len(demand_zones)} demand zones\")\n", "\n", "print(\"\\n📊 Technical indicators calculation completed!\")"]}, {"cell_type": "markdown", "metadata": {"id": "signal_labeling"}, "source": ["## 🎯 Signal Labeling & Target Generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "generate_signals"}, "outputs": [], "source": ["def generate_trading_signals(df, lookahead_periods=10, min_profit_pct=0.5, max_loss_pct=1.0):\n", "    \"\"\"\n", "    Generate Buy/Sell signals based on future price movements and technical conditions\n", "    \"\"\"\n", "    df = df.copy()\n", "    \n", "    # Initialize signal columns\n", "    df['Signal'] = 0  # 0: No signal, 1: Buy, -1: Sell\n", "    df['Entry_Price'] = df['Close']\n", "    df['Take_Profit'] = 0.0\n", "    df['Stop_Loss'] = 0.0\n", "    df['Signal_Strength'] = 0.0\n", "    \n", "    for i in range(len(df) - lookahead_periods):\n", "        current_price = df['Close'].iloc[i]\n", "        future_prices = df['Close'].iloc[i+1:i+lookahead_periods+1]\n", "        \n", "        if len(future_prices) == 0:\n", "            continue\n", "        \n", "        max_future_price = future_prices.max()\n", "        min_future_price = future_prices.min()\n", "        \n", "        # Calculate potential profit/loss percentages\n", "        max_profit_pct = ((max_future_price - current_price) / current_price) * 100\n", "        max_loss_pct = ((current_price - min_future_price) / current_price) * 100\n", "        \n", "        # Get current technical conditions\n", "        rsi = df['RSI'].iloc[i]\n", "        macd_bullish = df['MACD_Bullish'].iloc[i]\n", "        near_demand = df['Near_Demand_Zone'].iloc[i]\n", "        near_supply = df['Near_Supply_Zone'].iloc[i]\n", "        bb_position = df['BB_Position'].iloc[i]\n", "        atr = df['ATR'].iloc[i]\n", "        \n", "        # Skip if essential indicators are NaN\n", "        if pd.isna(rsi) or pd.isna(atr):\n", "            continue\n", "        \n", "        # BUY Signal Conditions\n", "        buy_conditions = [\n", "            rsi < 35,  # Oversold\n", "            macd_bullish == 1,  # MACD bullish\n", "            near_demand > 0.5,  # Near demand zone\n", "            bb_position < 0.2,  # Near lower Bollinger Band\n", "            max_profit_pct >= min_profit_pct  # Profitable future movement\n", "        ]\n", "        \n", "        # SELL Signal Conditions\n", "        sell_conditions = [\n", "            rsi > 65,  # Overbought\n", "            macd_bullish == 0,  # MACD bearish\n", "            near_supply > 0.5,  # Near supply zone\n", "            bb_position > 0.8,  # Near upper Bollinger Band\n", "            max_loss_pct >= min_profit_pct  # Profitable future movement (for sell)\n", "        ]\n", "        \n", "        # Calculate signal strength\n", "        buy_strength = sum(buy_conditions[:-1]) / (len(buy_conditions) - 1)\n", "        sell_strength = sum(sell_conditions[:-1]) / (len(sell_conditions) - 1)\n", "        \n", "        # Generate BUY signal\n", "        if sum(buy_conditions) >= 4:  # At least 4 conditions met\n", "            df.loc[df.index[i], 'Signal'] = 1\n", "            df.loc[df.index[i], 'Take_Profit'] = current_price * (1 + min_profit_pct/100)\n", "            df.loc[df.index[i], 'Stop_Loss'] = current_price * (1 - max_loss_pct/100)\n", "            df.loc[df.index[i], 'Signal_Strength'] = buy_strength\n", "        \n", "        # Generate SELL signal\n", "        elif sum(sell_conditions) >= 4:  # At least 4 conditions met\n", "            df.loc[df.index[i], 'Signal'] = -1\n", "            df.loc[df.index[i], 'Take_Profit'] = current_price * (1 - min_profit_pct/100)\n", "            df.loc[df.index[i], 'Stop_Loss'] = current_price * (1 + max_loss_pct/100)\n", "            df.loc[df.index[i], 'Signal_Strength'] = sell_strength\n", "    \n", "    return df\n", "\n", "# Generate signals for all timeframes\n", "print(\"🎯 Generating trading signals for all timeframes...\")\n", "\n", "labeled_data = {}\n", "\n", "for tf, df in processed_data.items():\n", "    print(f\"Generating signals for {tf}...\")\n", "    \n", "    # Adjust lookahead based on timeframe\n", "    lookahead_map = {\n", "        'M1': 30, 'M5': 20, 'M15': 15, 'M30': 10,\n", "        'H1': 8, 'H4': 5, 'D1': 3\n", "    }\n", "    \n", "    lookahead = lookahead_map.get(tf, 10)\n", "    df_labeled = generate_trading_signals(df, lookahead_periods=lookahead)\n", "    \n", "    # Remove rows with NaN values\n", "    df_labeled = df_labeled.dropna()\n", "    \n", "    labeled_data[tf] = df_labeled\n", "    \n", "    # Print signal statistics\n", "    buy_signals = (df_labeled['Signal'] == 1).sum()\n", "    sell_signals = (df_labeled['Signal'] == -1).sum()\n", "    total_signals = buy_signals + sell_signals\n", "    \n", "    print(f\"  ✅ {tf}: {buy_signals} BUY, {sell_signals} SELL, {total_signals} total signals\")\n", "    print(f\"     Signal rate: {(total_signals/len(df_labeled)*100):.2f}%\")\n", "\n", "print(\"\\n🎯 Signal generation completed!\")"]}, {"cell_type": "markdown", "metadata": {"id": "model_architectures"}, "source": ["## 🧠 AI Model Architectures"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "temporal_fusion_transformer"}, "outputs": [], "source": ["# Temporal Fusion Transformer for Price Prediction\n", "class TemporalFusionTransformer(nn.Module):\n", "    def __init__(self, input_size, hidden_size=128, num_heads=8, num_layers=4, dropout=0.1):\n", "        super(TemporalFusionTransformer, self).__init__()\n", "        \n", "        self.input_size = input_size\n", "        self.hidden_size = hidden_size\n", "        \n", "        # Input projection\n", "        self.input_projection = nn.Linear(input_size, hidden_size)\n", "        \n", "        # Positional encoding\n", "        self.positional_encoding = nn.Parameter(torch.randn(1000, hidden_size))\n", "        \n", "        # Multi-head attention layers\n", "        encoder_layer = nn.TransformerEncoderLayer(\n", "            d_model=hidden_size,\n", "            nhead=num_heads,\n", "            dim_feedforward=hidden_size * 4,\n", "            dropout=dropout,\n", "            batch_first=True\n", "        )\n", "        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)\n", "        \n", "        # Output layers\n", "        self.output_projection = nn.Sequential(\n", "            nn.Linear(hidden_size, hidden_size // 2),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(hidden_size // 2, 1)  # Price prediction\n", "        )\n", "        \n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, x):\n", "        batch_size, seq_len, _ = x.shape\n", "        \n", "        # Input projection\n", "        x = self.input_projection(x)\n", "        \n", "        # Add positional encoding\n", "        pos_enc = self.positional_encoding[:seq_len, :].unsqueeze(0).expand(batch_size, -1, -1)\n", "        x = x + pos_enc\n", "        x = self.dropout(x)\n", "        \n", "        # Transformer encoding\n", "        x = self.transformer(x)\n", "        \n", "        # Use last timestep for prediction\n", "        x = x[:, -1, :]\n", "        \n", "        # Output projection\n", "        output = self.output_projection(x)\n", "        \n", "        return output\n", "\n", "# CNN + BiLSTM + Attention for Signal Generation\n", "class CNNBiLSTMAttention(nn.Module):\n", "    def __init__(self, input_size, hidden_size=128, num_classes=3, dropout=0.1):\n", "        super(CNNBiLSTM<PERSON>tion, self).__init__()\n", "        \n", "        # CNN layers for feature extraction\n", "        self.conv1d_1 = nn.Conv1d(input_size, 64, kernel_size=3, padding=1)\n", "        self.conv1d_2 = nn.Conv1d(64, 128, kernel_size=3, padding=1)\n", "        self.conv1d_3 = nn.Conv1d(128, 64, kernel_size=3, padding=1)\n", "        \n", "        # BiLSTM layers\n", "        self.bilstm = nn.LSTM(\n", "            input_size=64,\n", "            hidden_size=hidden_size,\n", "            num_layers=2,\n", "            batch_first=True,\n", "            bidirectional=True,\n", "            dropout=dropout\n", "        )\n", "        \n", "        # Attention mechanism\n", "        self.attention = nn.Multihead<PERSON><PERSON>tion(\n", "            embed_dim=hidden_size * 2,\n", "            num_heads=8,\n", "            dropout=dropout,\n", "            batch_first=True\n", "        )\n", "        \n", "        # Classification head\n", "        self.classifier = nn.Sequential(\n", "            nn.Linear(hidden_size * 2, hidden_size),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(hidden_size, hidden_size // 2),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(hidden_size // 2, num_classes)  # Buy, <PERSON><PERSON>, Hold\n", "        )\n", "        \n", "        self.dropout = nn.Dropout(dropout)\n", "    \n", "    def forward(self, x):\n", "        batch_size, seq_len, features = x.shape\n", "        \n", "        # CNN feature extraction\n", "        x = x.transpose(1, 2)  # (batch, features, seq_len)\n", "        x = torch.relu(self.conv1d_1(x))\n", "        x = torch.relu(self.conv1d_2(x))\n", "        x = torch.relu(self.conv1d_3(x))\n", "        x = x.transpose(1, 2)  # (batch, seq_len, features)\n", "        \n", "        # BiLSTM\n", "        lstm_out, _ = self.bilstm(x)\n", "        \n", "        # Self-attention\n", "        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)\n", "        \n", "        # Global average pooling\n", "        pooled = torch.mean(attn_out, dim=1)\n", "        \n", "        # Classification\n", "        output = self.classifier(pooled)\n", "        \n", "        return output\n", "\n", "# Vision Transformer for Chart Pattern Recognition\n", "class VisionTransformerPatterns(nn.Module):\n", "    def __init__(self, image_size=224, patch_size=16, num_classes=10, dim=768, depth=12, heads=12, mlp_dim=3072):\n", "        super(VisionTransformerPatterns, self).__init__()\n", "        \n", "        self.patch_size = patch_size\n", "        self.num_patches = (image_size // patch_size) ** 2\n", "        self.patch_dim = 3 * patch_size ** 2  # RGB channels\n", "        \n", "        # Patch embedding\n", "        self.patch_embedding = nn.Linear(self.patch_dim, dim)\n", "        \n", "        # Positional embedding\n", "        self.pos_embedding = nn.Parameter(torch.randn(1, self.num_patches + 1, dim))\n", "        \n", "        # Class token\n", "        self.cls_token = nn.Parameter(torch.randn(1, 1, dim))\n", "        \n", "        # Transformer encoder\n", "        encoder_layer = nn.TransformerEncoderLayer(\n", "            d_model=dim,\n", "            nhead=heads,\n", "            dim_feedforward=mlp_dim,\n", "            dropout=0.1,\n", "            batch_first=True\n", "        )\n", "        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=depth)\n", "        \n", "        # Classification head\n", "        self.classifier = nn.Sequential(\n", "            nn.<PERSON><PERSON><PERSON><PERSON>(dim),\n", "            nn.Linear(dim, num_classes)\n", "        )\n", "    \n", "    def forward(self, x):\n", "        batch_size = x.shape[0]\n", "        \n", "        # Extract patches\n", "        patches = self.extract_patches(x)\n", "        \n", "        # Patch embedding\n", "        x = self.patch_embedding(patches)\n", "        \n", "        # Add class token\n", "        cls_tokens = self.cls_token.expand(batch_size, -1, -1)\n", "        x = torch.cat([cls_tokens, x], dim=1)\n", "        \n", "        # Add positional embedding\n", "        x += self.pos_embedding\n", "        \n", "        # Transformer encoding\n", "        x = self.transformer(x)\n", "        \n", "        # Classification using class token\n", "        cls_output = x[:, 0]\n", "        output = self.classifier(cls_output)\n", "        \n", "        return output\n", "    \n", "    def extract_patches(self, x):\n", "        batch_size, channels, height, width = x.shape\n", "        patches = x.unfold(2, self.patch_size, self.patch_size).unfold(3, self.patch_size, self.patch_size)\n", "        patches = patches.contiguous().view(batch_size, channels, -1, self.patch_size, self.patch_size)\n", "        patches = patches.permute(0, 2, 1, 3, 4).contiguous()\n", "        patches = patches.view(batch_size, -1, channels * self.patch_size * self.patch_size)\n", "        return patches\n", "\n", "print(\"🧠 AI model architectures defined successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "siamese_networks"}, "source": ["## 🔗 Siamese Networks for Pattern Similarity"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "siamese_implementation"}, "outputs": [], "source": ["# Siamese Network for Pattern Similarity Matching\n", "class SiameseNetwork(nn.Module):\n", "    def __init__(self, input_size, embedding_dim=128):\n", "        super(SiameseNetwork, self).__init__()\n", "        \n", "        # Shared encoder network\n", "        self.encoder = nn.Sequential(\n", "            nn.Linear(input_size, 256),\n", "            nn.ReLU(),\n", "            nn.Dropout(0.2),\n", "            nn.<PERSON>(256, 128),\n", "            nn.ReLU(),\n", "            nn.Dropout(0.2),\n", "            nn.<PERSON><PERSON>(128, embedding_dim),\n", "            nn.<PERSON>h()  # Normalize embeddings\n", "        )\n", "        \n", "        # Similarity classifier\n", "        self.similarity_classifier = nn.Sequential(\n", "            nn.<PERSON>ar(embedding_dim, 64),\n", "            nn.ReLU(),\n", "            nn.Dropout(0.1),\n", "            nn.<PERSON><PERSON>(64, 1),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>()\n", "        )\n", "    \n", "    def forward_one(self, x):\n", "        \"\"\"Forward pass for one input\"\"\"\n", "        return self.encoder(x)\n", "    \n", "    def forward(self, x1, x2):\n", "        \"\"\"Forward pass for pair of inputs\"\"\"\n", "        # Get embeddings\n", "        embedding1 = self.forward_one(x1)\n", "        embedding2 = self.forward_one(x2)\n", "        \n", "        # Calculate absolute difference\n", "        diff = torch.abs(embedding1 - embedding2)\n", "        \n", "        # Similarity score\n", "        similarity = self.similarity_classifier(diff)\n", "        \n", "        return similarity, embedding1, embedding2\n", "\n", "# Confidence Synthesizer - Final Decision Engine\n", "class ConfidenceSynthesizer(nn.Module):\n", "    def __init__(self, input_features=10, hidden_size=64, num_classes=3):\n", "        super(ConfidenceSynthesizer, self).__init__()\n", "        \n", "        self.feature_processor = nn.Sequential(\n", "            nn.Linear(input_features, hidden_size),\n", "            nn.ReLU(),\n", "            nn.BatchNorm1d(hidden_size),\n", "            nn.Dropout(0.2),\n", "            nn.Linear(hidden_size, hidden_size // 2),\n", "            nn.ReLU(),\n", "            nn.Dropout(0.1)\n", "        )\n", "        \n", "        # Final decision layer\n", "        self.decision_layer = nn.Sequential(\n", "            nn.Linear(hidden_size // 2, num_classes),\n", "            nn.<PERSON><PERSON>(dim=1)\n", "        )\n", "        \n", "        # Confidence estimation\n", "        self.confidence_estimator = nn.Sequential(\n", "            nn.Linear(hidden_size // 2, 16),\n", "            nn.ReLU(),\n", "            nn.<PERSON><PERSON>(16, 1),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>()\n", "        )\n", "    \n", "    def forward(self, x):\n", "        # Process input features\n", "        features = self.feature_processor(x)\n", "        \n", "        # Get decision probabilities\n", "        decision_probs = self.decision_layer(features)\n", "        \n", "        # Get confidence score\n", "        confidence = self.confidence_estimator(features)\n", "        \n", "        return decision_probs, confidence\n", "\n", "print(\"🔗 Siamese Networks and Confidence Synthesizer defined!\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_preparation"}, "source": ["## 📊 Data Preparation for Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "prepare_training_data"}, "outputs": [], "source": ["def prepare_sequences(df, sequence_length=60, target_col='Close'):\n", "    \"\"\"\n", "    Prepare sequences for time series modeling\n", "    \"\"\"\n", "    # Select feature columns (exclude target and non-numeric columns)\n", "    feature_cols = [col for col in df.columns if col not in [\n", "        'Signal', 'Entry_Price', 'Take_Profit', 'Stop_Loss', 'Timeframe',\n", "        '<PERSON>vo<PERSON>_High', '<PERSON>vot_Low'\n", "    ] and df[col].dtype in ['float64', 'int64']]\n", "    \n", "    # Fill any remaining NaN values\n", "    df_clean = df[feature_cols + ['Signal']].fillna(method='ffill').fillna(0)\n", "    \n", "    # Normalize features\n", "    scaler = StandardScaler()\n", "    scaled_features = scaler.fit_transform(df_clean[feature_cols])\n", "    \n", "    # Create sequences\n", "    X, y_price, y_signal = [], [], []\n", "    \n", "    for i in range(sequence_length, len(scaled_features)):\n", "        # Input sequence\n", "        X.append(scaled_features[i-sequence_length:i])\n", "        \n", "        # Price prediction target (next close price)\n", "        if i < len(df_clean) - 1:\n", "            y_price.append(df_clean[target_col].iloc[i+1])\n", "        else:\n", "            y_price.append(df_clean[target_col].iloc[i])\n", "        \n", "        # Signal classification target\n", "        signal = df_clean['Signal'].iloc[i]\n", "        # Convert to classification: -1->0, 0->1, 1->2\n", "        y_signal.append(signal + 1)\n", "    \n", "    return np.array(X), np.array(y_price), np.array(y_signal), scaler, feature_cols\n", "\n", "def create_pattern_pairs(sequences, labels, num_pairs=10000):\n", "    \"\"\"\n", "    Create pairs for Siamese network training\n", "    \"\"\"\n", "    pairs = []\n", "    pair_labels = []\n", "    \n", "    # Flatten sequences for similarity comparison\n", "    flattened_sequences = sequences.reshape(len(sequences), -1)\n", "    \n", "    for _ in range(num_pairs):\n", "        # Random pair selection\n", "        idx1, idx2 = np.random.choice(len(sequences), 2, replace=False)\n", "        \n", "        pairs.append([flattened_sequences[idx1], flattened_sequences[idx2]])\n", "        \n", "        # Label: 1 if same signal, 0 if different\n", "        pair_labels.append(1 if labels[idx1] == labels[idx2] else 0)\n", "    \n", "    return np.array(pairs), np.array(pair_labels)\n", "\n", "# Prepare training data for all timeframes\n", "print(\"📊 Preparing training data for all timeframes...\")\n", "\n", "training_data = {}\n", "scalers = {}\n", "\n", "for tf, df in labeled_data.items():\n", "    print(f\"\\nPreparing {tf} data...\")\n", "    \n", "    # Prepare sequences\n", "    X, y_price, y_signal, scaler, feature_cols = prepare_sequences(df, sequence_length=60)\n", "    \n", "    # Split data\n", "    split_idx = int(0.8 * len(X))\n", "    \n", "    X_train, X_test = X[:split_idx], X[split_idx:]\n", "    y_price_train, y_price_test = y_price[:split_idx], y_price[split_idx:]\n", "    y_signal_train, y_signal_test = y_signal[:split_idx], y_signal[split_idx:]\n", "    \n", "    # Create pattern pairs for Siamese network\n", "    pairs_train, pair_labels_train = create_pattern_pairs(X_train, y_signal_train, num_pairs=5000)\n", "    pairs_test, pair_labels_test = create_pattern_pairs(X_test, y_signal_test, num_pairs=1000)\n", "    \n", "    training_data[tf] = {\n", "        'X_train': X_train, 'X_test': X_test,\n", "        'y_price_train': y_price_train, 'y_price_test': y_price_test,\n", "        'y_signal_train': y_signal_train, 'y_signal_test': y_signal_test,\n", "        'pairs_train': pairs_train, 'pairs_test': pairs_test,\n", "        'pair_labels_train': pair_labels_train, 'pair_labels_test': pair_labels_test,\n", "        'feature_cols': feature_cols\n", "    }\n", "    \n", "    scalers[tf] = scaler\n", "    \n", "    print(f\"  ✅ {tf}: {len(X_train)} train, {len(X_test)} test sequences\")\n", "    print(f\"     Features: {len(feature_cols)}, Sequence length: {X_train.shape[1]}\")\n", "    print(f\"     Signal distribution: {np.bincount(y_signal_train)}\")\n", "\n", "print(\"\\n📊 Data preparation completed!\")"]}, {"cell_type": "markdown", "metadata": {"id": "progress_tracking"}, "source": ["## 📊 Training Progress Tracking System"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "progress_utilities"}, "outputs": [], "source": ["# Enhanced Training Progress Tracking\n", "class TrainingProgressTracker:\n", "    def __init__(self, model_name, total_epochs):\n", "        self.model_name = model_name\n", "        self.total_epochs = total_epochs\n", "        self.start_time = time.time()\n", "        \n", "        # Create progress widgets\n", "        self.epoch_progress = widgets.IntProgress(\n", "            value=0,\n", "            min=0,\n", "            max=total_epochs,\n", "            description='Epochs:',\n", "            bar_style='info',\n", "            style={'bar_color': '#1f77b4'},\n", "            layout=widgets.Layout(width='100%')\n", "        )\n", "        \n", "        self.batch_progress = widgets.IntProgress(\n", "            value=0,\n", "            min=0,\n", "            max=100,\n", "            description='Batches:',\n", "            bar_style='success',\n", "            style={'bar_color': '#2ca02c'},\n", "            layout=widgets.Layout(width='100%')\n", "        )\n", "        \n", "        self.status_text = widgets.HTML(\n", "            value=f\"<h3>🧠 Training {model_name}</h3>\",\n", "            layout=widgets.Layout(width='100%')\n", "        )\n", "        \n", "        self.metrics_text = widgets.HTML(\n", "            value=\"<p>Initializing training...</p>\",\n", "            layout=widgets.Layout(width='100%')\n", "        )\n", "        \n", "        self.time_text = widgets.HTML(\n", "            value=\"<p>⏱️ Time: 00:00:00</p>\",\n", "            layout=widgets.Layout(width='100%')\n", "        )\n", "        \n", "        # Create layout\n", "        self.progress_box = widgets.VBox([\n", "            self.status_text,\n", "            self.epoch_progress,\n", "            self.batch_progress,\n", "            self.metrics_text,\n", "            self.time_text\n", "        ])\n", "        \n", "        # Display the widget\n", "        display(self.progress_box)\n", "        \n", "        # Store metrics for plotting\n", "        self.train_losses = []\n", "        self.test_losses = []\n", "        self.train_accuracies = []\n", "        self.test_accuracies = []\n", "        \n", "    def update_epoch(self, epoch, train_loss, test_loss, train_acc=None, test_acc=None):\n", "        \"\"\"Update progress for completed epoch\"\"\"\n", "        self.epoch_progress.value = epoch + 1\n", "        \n", "        # Store metrics\n", "        self.train_losses.append(train_loss)\n", "        self.test_losses.append(test_loss)\n", "        if train_acc is not None:\n", "            self.train_accuracies.append(train_acc)\n", "        if test_acc is not None:\n", "            self.test_accuracies.append(test_acc)\n", "        \n", "        # Update time\n", "        elapsed_time = time.time() - self.start_time\n", "        hours, remainder = divmod(elapsed_time, 3600)\n", "        minutes, seconds = divmod(remainder, 60)\n", "        time_str = f\"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}\"\n", "        \n", "        # Estimate remaining time\n", "        if epoch > 0:\n", "            avg_time_per_epoch = elapsed_time / (epoch + 1)\n", "            remaining_epochs = self.total_epochs - (epoch + 1)\n", "            remaining_time = avg_time_per_epoch * remaining_epochs\n", "            rem_hours, rem_remainder = divmod(remaining_time, 3600)\n", "            rem_minutes, rem_seconds = divmod(rem_remainder, 60)\n", "            remaining_str = f\"{int(rem_hours):02d}:{int(rem_minutes):02d}:{int(rem_seconds):02d}\"\n", "        else:\n", "            remaining_str = \"--:--:--\"\n", "        \n", "        self.time_text.value = f\"<p>⏱️ Elapsed: {time_str} | 🔮 Remaining: {remaining_str}</p>\"\n", "        \n", "        # Update metrics display\n", "        if train_acc is not None and test_acc is not None:\n", "            metrics_html = f\"\"\"\n", "            <div style='background-color: #f0f0f0; padding: 10px; border-radius: 5px; margin: 5px 0;'>\n", "                <p><strong>📊 Epoch {epoch + 1}/{self.total_epochs}</strong></p>\n", "                <p>🔻 Train Loss: <span style='color: #d62728;'>{train_loss:.6f}</span> | \n", "                   🔻 Test Loss: <span style='color: #ff7f0e;'>{test_loss:.6f}</span></p>\n", "                <p>✅ Train Acc: <span style='color: #2ca02c;'>{train_acc:.4f}</span> | \n", "                   ✅ Test Acc: <span style='color: #1f77b4;'>{test_acc:.4f}</span></p>\n", "            </div>\n", "            \"\"\"\n", "        else:\n", "            metrics_html = f\"\"\"\n", "            <div style='background-color: #f0f0f0; padding: 10px; border-radius: 5px; margin: 5px 0;'>\n", "                <p><strong>📊 Epoch {epoch + 1}/{self.total_epochs}</strong></p>\n", "                <p>🔻 Train Loss: <span style='color: #d62728;'>{train_loss:.6f}</span> | \n", "                   🔻 Test Loss: <span style='color: #ff7f0e;'>{test_loss:.6f}</span></p>\n", "            </div>\n", "            \"\"\"\n", "        \n", "        self.metrics_text.value = metrics_html\n", "        \n", "        # Update progress bar color based on performance\n", "        if len(self.test_losses) > 1:\n", "            if self.test_losses[-1] < self.test_losses[-2]:\n", "                self.epoch_progress.bar_style = 'success'\n", "            else:\n", "                self.epoch_progress.bar_style = 'warning'\n", "    \n", "    def update_batch(self, batch_idx, total_batches, current_loss):\n", "        \"\"\"Update progress within epoch\"\"\"\n", "        progress_pct = int((batch_idx / total_batches) * 100)\n", "        self.batch_progress.value = progress_pct\n", "        self.batch_progress.description = f'Batch {batch_idx}/{total_batches} (Loss: {current_loss:.4f})'\n", "    \n", "    def complete_training(self):\n", "        \"\"\"Mark training as complete\"\"\"\n", "        self.epoch_progress.bar_style = 'success'\n", "        self.batch_progress.bar_style = 'success'\n", "        self.batch_progress.value = 100\n", "        \n", "        total_time = time.time() - self.start_time\n", "        hours, remainder = divmod(total_time, 3600)\n", "        minutes, seconds = divmod(remainder, 60)\n", "        time_str = f\"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}\"\n", "        \n", "        self.status_text.value = f\"<h3>✅ {self.model_name} Training Complete!</h3>\"\n", "        self.time_text.value = f\"<p>🎉 Total Training Time: {time_str}</p>\"\n", "        \n", "        # Show final performance\n", "        if self.train_accuracies:\n", "            final_metrics = f\"\"\"\n", "            <div style='background-color: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>\n", "                <h4>🏆 Final Performance</h4>\n", "                <p>🔻 Final Test Loss: <strong>{self.test_losses[-1]:.6f}</strong></p>\n", "                <p>✅ Final Test Accuracy: <strong>{self.test_accuracies[-1]:.4f}</strong></p>\n", "                <p>📈 Best Test Accuracy: <strong>{max(self.test_accuracies):.4f}</strong></p>\n", "            </div>\n", "            \"\"\"\n", "        else:\n", "            final_metrics = f\"\"\"\n", "            <div style='background-color: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>\n", "                <h4>🏆 Final Performance</h4>\n", "                <p>🔻 Final Test Loss: <strong>{self.test_losses[-1]:.6f}</strong></p>\n", "                <p>📉 Best Test Loss: <strong>{min(self.test_losses):.6f}</strong></p>\n", "            </div>\n", "            \"\"\"\n", "        \n", "        self.metrics_text.value = final_metrics\n", "    \n", "    def plot_training_curves(self):\n", "        \"\"\"Plot training curves in real-time\"\"\"\n", "        if len(self.train_losses) < 2:\n", "            return\n", "        \n", "        fig, axes = plt.subplots(1, 2 if self.train_accuracies else 1, figsize=(15, 5))\n", "        if not self.train_accuracies:\n", "            axes = [axes]\n", "        \n", "        # Plot losses\n", "        epochs = range(1, len(self.train_losses) + 1)\n", "        axes[0].plot(epochs, self.train_losses, 'b-', label='Train Loss', linewidth=2)\n", "        axes[0].plot(epochs, self.test_losses, 'r-', label='Test Loss', linewidth=2)\n", "        axes[0].set_title(f'{self.model_name} - Training Loss')\n", "        axes[0].set_xlabel('Epoch')\n", "        axes[0].set_ylabel('Loss')\n", "        axes[0].legend()\n", "        axes[0].grid(True, alpha=0.3)\n", "        \n", "        # Plot accuracies if available\n", "        if self.train_accuracies and len(axes) > 1:\n", "            axes[1].plot(epochs, self.train_accuracies, 'g-', label='Train Accuracy', linewidth=2)\n", "            axes[1].plot(epochs, self.test_accuracies, 'orange', label='Test Accuracy', linewidth=2)\n", "            axes[1].set_title(f'{self.model_name} - Training Accuracy')\n", "            axes[1].set_xlabel('Epoch')\n", "            axes[1].set_ylabel('Accuracy')\n", "            axes[1].legend()\n", "            axes[1].grid(True, alpha=0.3)\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "# Live plotting function\n", "def create_live_plot():\n", "    \"\"\"Create live updating plot for training metrics\"\"\"\n", "    fig, ax = plt.subplots(figsize=(12, 6))\n", "    return fig, ax\n", "\n", "def update_live_plot(fig, ax, train_losses, test_losses, model_name, train_acc=None, test_acc=None):\n", "    \"\"\"Update live plot with new data\"\"\"\n", "    ax.clear()\n", "    \n", "    epochs = range(1, len(train_losses) + 1)\n", "    \n", "    # Plot losses\n", "    ax.plot(epochs, train_losses, 'b-', label='Train Loss', linewidth=2, marker='o')\n", "    ax.plot(epochs, test_losses, 'r-', label='Test Loss', linewidth=2, marker='s')\n", "    \n", "    ax.set_title(f'{model_name} - Training Progress (Real-time)', fontsize=14, fontweight='bold')\n", "    ax.set_xlabel('Epoch', fontsize=12)\n", "    ax.set_ylabel('Loss', fontsize=12)\n", "    ax.legend(fontsize=10)\n", "    ax.grid(True, alpha=0.3)\n", "    \n", "    # Add current values as text\n", "    if train_losses and test_losses:\n", "        current_info = f'Current - Train: {train_losses[-1]:.4f}, Test: {test_losses[-1]:.4f}'\n", "        if train_acc and test_acc:\n", "            current_info += f'\\nAccuracy - Train: {train_acc[-1]:.3f}, Test: {test_acc[-1]:.3f}'\n", "        \n", "        ax.text(0.02, 0.98, current_info, transform=ax.transAxes, \n", "                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))\n", "    \n", "    plt.tight_layout()\n", "    display(fig)\n", "    clear_output(wait=True)\n", "\n", "print(\"📊 Training progress tracking system ready!\")"]}, {"cell_type": "markdown", "metadata": {"id": "model_training"}, "source": ["## 🚀 Enhanced Model Training Pipeline"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "training_functions"}, "outputs": [], "source": ["def train_temporal_fusion_transformer(X_train, y_train, X_test, y_test, epochs=50):\n", "    \"\"\"\n", "    Train Temporal Fusion Transformer for price prediction\n", "    \"\"\"\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    print(f\"Training TFT on {device}\")\n", "    \n", "    # Model initialization\n", "    input_size = X_train.shape[2]\n", "    model = TemporalFusionTransformer(input_size=input_size).to(device)\n", "    \n", "    # Loss and optimizer\n", "    criterion = nn.MS<PERSON><PERSON>()\n", "    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)\n", "    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)\n", "    \n", "    # Convert to tensors\n", "    X_train_tensor = torch.FloatTensor(X_train).to(device)\n", "    y_train_tensor = torch.FloatTensor(y_train).unsqueeze(1).to(device)\n", "    X_test_tensor = torch.FloatTensor(X_test).to(device)\n", "    y_test_tensor = torch.FloatTensor(y_test).unsqueeze(1).to(device)\n", "    \n", "    # Training loop\n", "    train_losses = []\n", "    test_losses = []\n", "    \n", "    batch_size = 32\n", "    \n", "    for epoch in range(epochs):\n", "        model.train()\n", "        epoch_train_loss = 0\n", "        \n", "        # Mini-batch training\n", "        for i in range(0, len(X_train_tensor), batch_size):\n", "            batch_X = X_train_tensor[i:i+batch_size]\n", "            batch_y = y_train_tensor[i:i+batch_size]\n", "            \n", "            optimizer.zero_grad()\n", "            outputs = model(batch_X)\n", "            loss = criterion(outputs, batch_y)\n", "            loss.backward()\n", "            optimizer.step()\n", "            \n", "            epoch_train_loss += loss.item()\n", "        \n", "        # Validation\n", "        model.eval()\n", "        with torch.no_grad():\n", "            test_outputs = model(X_test_tensor)\n", "            test_loss = criterion(test_outputs, y_test_tensor)\n", "        \n", "        avg_train_loss = epoch_train_loss / (len(X_train_tensor) // batch_size)\n", "        train_losses.append(avg_train_loss)\n", "        test_losses.append(test_loss.item())\n", "        \n", "        scheduler.step(test_loss)\n", "        \n", "        if epoch % 10 == 0:\n", "            print(f\"Epoch {epoch}: Train Loss: {avg_train_loss:.6f}, Test Loss: {test_loss.item():.6f}\")\n", "    \n", "    return model, train_losses, test_losses\n", "\n", "def train_cnn_bilstm_attention(X_train, y_train, X_test, y_test, epochs=50):\n", "    \"\"\"\n", "    Train CNN + BiLSTM + Attention for signal generation\n", "    \"\"\"\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    print(f\"Training CNN+BiLSTM+Attention on {device}\")\n", "    \n", "    # Model initialization\n", "    input_size = X_train.shape[2]\n", "    model = CNNBiLSTMAttention(input_size=input_size, num_classes=3).to(device)\n", "    \n", "    # Loss and optimizer\n", "    criterion = nn.CrossEntropyLoss()\n", "    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)\n", "    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)\n", "    \n", "    # Convert to tensors\n", "    X_train_tensor = torch.FloatTensor(X_train).to(device)\n", "    y_train_tensor = torch.LongTensor(y_train).to(device)\n", "    X_test_tensor = torch.FloatTensor(X_test).to(device)\n", "    y_test_tensor = torch.LongTensor(y_test).to(device)\n", "    \n", "    # Training loop\n", "    train_losses = []\n", "    test_losses = []\n", "    train_accuracies = []\n", "    test_accuracies = []\n", "    \n", "    batch_size = 32\n", "    \n", "    for epoch in range(epochs):\n", "        model.train()\n", "        epoch_train_loss = 0\n", "        correct_train = 0\n", "        total_train = 0\n", "        \n", "        # Mini-batch training\n", "        for i in range(0, len(X_train_tensor), batch_size):\n", "            batch_X = X_train_tensor[i:i+batch_size]\n", "            batch_y = y_train_tensor[i:i+batch_size]\n", "            \n", "            optimizer.zero_grad()\n", "            outputs = model(batch_X)\n", "            loss = criterion(outputs, batch_y)\n", "            loss.backward()\n", "            optimizer.step()\n", "            \n", "            epoch_train_loss += loss.item()\n", "            \n", "            # Calculate accuracy\n", "            _, predicted = torch.max(outputs.data, 1)\n", "            total_train += batch_y.size(0)\n", "            correct_train += (predicted == batch_y).sum().item()\n", "        \n", "        # Validation\n", "        model.eval()\n", "        with torch.no_grad():\n", "            test_outputs = model(X_test_tensor)\n", "            test_loss = criterion(test_outputs, y_test_tensor)\n", "            \n", "            _, predicted_test = torch.max(test_outputs.data, 1)\n", "            test_accuracy = (predicted_test == y_test_tensor).sum().item() / len(y_test_tensor)\n", "        \n", "        avg_train_loss = epoch_train_loss / (len(X_train_tensor) // batch_size)\n", "        train_accuracy = correct_train / total_train\n", "        \n", "        train_losses.append(avg_train_loss)\n", "        test_losses.append(test_loss.item())\n", "        train_accuracies.append(train_accuracy)\n", "        test_accuracies.append(test_accuracy)\n", "        \n", "        scheduler.step(test_loss)\n", "        \n", "        if epoch % 10 == 0:\n", "            print(f\"Epoch {epoch}: Train Loss: {avg_train_loss:.4f}, Test Loss: {test_loss.item():.4f}\")\n", "            print(f\"         Train Acc: {train_accuracy:.4f}, Test Acc: {test_accuracy:.4f}\")\n", "    \n", "    return model, train_losses, test_losses, train_accuracies, test_accuracies\n", "\n", "print(\"🚀 Training functions defined!\")"]}, {"cell_type": "markdown", "metadata": {"id": "enhanced_training"}, "source": ["## 🎯 Enhanced Training Functions with Progress Tracking"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "enhanced_training_functions"}, "outputs": [], "source": ["# Enhanced training functions with progress tracking\n", "def train_tft_with_progress(X_train, y_train, X_test, y_test, epochs=50, timeframe=''):\n", "    \"\"\"\n", "    Train Temporal Fusion Transformer with enhanced progress tracking\n", "    \"\"\"\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    model_name = f'TFT ({timeframe})' if timeframe else 'Temporal Fusion Transformer'\n", "    \n", "    # Initialize progress tracker\n", "    progress_tracker = TrainingProgressTracker(model_name, epochs)\n", "    \n", "    # Model initialization\n", "    input_size = X_train.shape[2]\n", "    model = TemporalFusionTransformer(input_size=input_size).to(device)\n", "    \n", "    # Loss and optimizer\n", "    criterion = nn.MS<PERSON><PERSON>()\n", "    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)\n", "    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)\n", "    \n", "    # Convert to tensors\n", "    X_train_tensor = torch.FloatTensor(X_train).to(device)\n", "    y_train_tensor = torch.FloatTensor(y_train).unsqueeze(1).to(device)\n", "    X_test_tensor = torch.FloatTensor(X_test).to(device)\n", "    y_test_tensor = torch.FloatTensor(y_test).unsqueeze(1).to(device)\n", "    \n", "    # Training loop\n", "    train_losses = []\n", "    test_losses = []\n", "    batch_size = 32\n", "    total_batches = len(X_train_tensor) // batch_size\n", "    \n", "    for epoch in range(epochs):\n", "        model.train()\n", "        epoch_train_loss = 0\n", "        \n", "        # Mini-batch training with progress tracking\n", "        for i, batch_start in enumerate(range(0, len(X_train_tensor), batch_size)):\n", "            batch_X = X_train_tensor[batch_start:batch_start+batch_size]\n", "            batch_y = y_train_tensor[batch_start:batch_start+batch_size]\n", "            \n", "            optimizer.zero_grad()\n", "            outputs = model(batch_X)\n", "            loss = criterion(outputs, batch_y)\n", "            loss.backward()\n", "            optimizer.step()\n", "            \n", "            batch_loss = loss.item()\n", "            epoch_train_loss += batch_loss\n", "            \n", "            # Update batch progress every 10 batches\n", "            if i % 10 == 0 or i == total_batches - 1:\n", "                progress_tracker.update_batch(i + 1, total_batches, batch_loss)\n", "        \n", "        # Validation\n", "        model.eval()\n", "        with torch.no_grad():\n", "            test_outputs = model(X_test_tensor)\n", "            test_loss = criterion(test_outputs, y_test_tensor)\n", "        \n", "        avg_train_loss = epoch_train_loss / total_batches\n", "        train_losses.append(avg_train_loss)\n", "        test_losses.append(test_loss.item())\n", "        \n", "        # Update progress tracker\n", "        progress_tracker.update_epoch(epoch, avg_train_loss, test_loss.item())\n", "        \n", "        scheduler.step(test_loss)\n", "    \n", "    # Complete training\n", "    progress_tracker.complete_training()\n", "    progress_tracker.plot_training_curves()\n", "    \n", "    return model, train_losses, test_losses\n", "\n", "def train_signal_model_with_progress(X_train, y_train, X_test, y_test, epochs=50, timeframe=''):\n", "    \"\"\"\n", "    Train CNN + BiLSTM + Attention with enhanced progress tracking\n", "    \"\"\"\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    model_name = f'Signal Model ({timeframe})' if timeframe else 'CNN+BiLSTM+Attention'\n", "    \n", "    # Initialize progress tracker\n", "    progress_tracker = TrainingProgressTracker(model_name, epochs)\n", "    \n", "    # Model initialization\n", "    input_size = X_train.shape[2]\n", "    model = CNNBiLSTMAttention(input_size=input_size, num_classes=3).to(device)\n", "    \n", "    # Loss and optimizer\n", "    criterion = nn.CrossEntropyLoss()\n", "    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)\n", "    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)\n", "    \n", "    # Convert to tensors\n", "    X_train_tensor = torch.FloatTensor(X_train).to(device)\n", "    y_train_tensor = torch.LongTensor(y_train).to(device)\n", "    X_test_tensor = torch.FloatTensor(X_test).to(device)\n", "    y_test_tensor = torch.LongTensor(y_test).to(device)\n", "    \n", "    # Training loop\n", "    train_losses = []\n", "    test_losses = []\n", "    train_accuracies = []\n", "    test_accuracies = []\n", "    \n", "    batch_size = 32\n", "    total_batches = len(X_train_tensor) // batch_size\n", "    \n", "    for epoch in range(epochs):\n", "        model.train()\n", "        epoch_train_loss = 0\n", "        correct_train = 0\n", "        total_train = 0\n", "        \n", "        # Mini-batch training with progress tracking\n", "        for i, batch_start in enumerate(range(0, len(X_train_tensor), batch_size)):\n", "            batch_X = X_train_tensor[batch_start:batch_start+batch_size]\n", "            batch_y = y_train_tensor[batch_start:batch_start+batch_size]\n", "            \n", "            optimizer.zero_grad()\n", "            outputs = model(batch_X)\n", "            loss = criterion(outputs, batch_y)\n", "            loss.backward()\n", "            optimizer.step()\n", "            \n", "            batch_loss = loss.item()\n", "            epoch_train_loss += batch_loss\n", "            \n", "            # Calculate accuracy\n", "            _, predicted = torch.max(outputs.data, 1)\n", "            total_train += batch_y.size(0)\n", "            correct_train += (predicted == batch_y).sum().item()\n", "            \n", "            # Update batch progress every 10 batches\n", "            if i % 10 == 0 or i == total_batches - 1:\n", "                current_acc = correct_train / total_train if total_train > 0 else 0\n", "                progress_tracker.update_batch(i + 1, total_batches, batch_loss)\n", "        \n", "        # Validation\n", "        model.eval()\n", "        with torch.no_grad():\n", "            test_outputs = model(X_test_tensor)\n", "            test_loss = criterion(test_outputs, y_test_tensor)\n", "            \n", "            _, predicted_test = torch.max(test_outputs.data, 1)\n", "            test_accuracy = (predicted_test == y_test_tensor).sum().item() / len(y_test_tensor)\n", "        \n", "        avg_train_loss = epoch_train_loss / total_batches\n", "        train_accuracy = correct_train / total_train\n", "        \n", "        train_losses.append(avg_train_loss)\n", "        test_losses.append(test_loss.item())\n", "        train_accuracies.append(train_accuracy)\n", "        test_accuracies.append(test_accuracy)\n", "        \n", "        # Update progress tracker\n", "        progress_tracker.update_epoch(epoch, avg_train_loss, test_loss.item(), train_accuracy, test_accuracy)\n", "        \n", "        scheduler.step(test_loss)\n", "    \n", "    # Complete training\n", "    progress_tracker.complete_training()\n", "    progress_tracker.plot_training_curves()\n", "    \n", "    return model, train_losses, test_losses, train_accuracies, test_accuracies\n", "\n", "def train_siamese_with_progress(pairs_train, pair_labels_train, epochs=20, timeframe=''):\n", "    \"\"\"\n", "    Train Siamese Network with enhanced progress tracking\n", "    \"\"\"\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    model_name = f'Siamese ({timeframe})' if timeframe else 'Siamese Network'\n", "    \n", "    # Initialize progress tracker\n", "    progress_tracker = TrainingProgressTracker(model_name, epochs)\n", "    \n", "    # Model initialization\n", "    input_size = pairs_train.shape[2]  # Flattened sequence size\n", "    model = SiameseNetwork(input_size=input_size).to(device)\n", "    \n", "    # Loss and optimizer\n", "    criterion = nn.<PERSON><PERSON><PERSON>()\n", "    optimizer = optim.AdamW(model.parameters(), lr=0.001)\n", "    \n", "    # Convert to tensors\n", "    pairs_train_tensor = torch.FloatTensor(pairs_train).to(device)\n", "    pair_labels_train_tensor = torch.FloatTensor(pair_labels_train).unsqueeze(1).to(device)\n", "    \n", "    siamese_losses = []\n", "    batch_size = 32\n", "    total_batches = len(pairs_train_tensor) // batch_size\n", "    \n", "    for epoch in range(epochs):\n", "        model.train()\n", "        epoch_loss = 0\n", "        \n", "        for i, batch_start in enumerate(range(0, len(pairs_train_tensor), batch_size)):\n", "            batch_pairs = pairs_train_tensor[batch_start:batch_start+batch_size]\n", "            batch_labels = pair_labels_train_tensor[batch_start:batch_start+batch_size]\n", "            \n", "            optimizer.zero_grad()\n", "            \n", "            # Forward pass\n", "            similarity, _, _ = model(batch_pairs[:, 0], batch_pairs[:, 1])\n", "            loss = criterion(similarity, batch_labels)\n", "            \n", "            loss.backward()\n", "            optimizer.step()\n", "            \n", "            batch_loss = loss.item()\n", "            epoch_loss += batch_loss\n", "            \n", "            # Update batch progress every 10 batches\n", "            if i % 10 == 0 or i == total_batches - 1:\n", "                progress_tracker.update_batch(i + 1, total_batches, batch_loss)\n", "        \n", "        avg_loss = epoch_loss / total_batches\n", "        siamese_losses.append(avg_loss)\n", "        \n", "        # Update progress tracker (using loss as both train and test for simplicity)\n", "        progress_tracker.update_epoch(epoch, avg_loss, avg_loss)\n", "    \n", "    # Complete training\n", "    progress_tracker.complete_training()\n", "    progress_tracker.plot_training_curves()\n", "    \n", "    return model, siamese_losses\n", "\n", "print(\"🎯 Enhanced training functions with progress tracking ready!\")"]}, {"cell_type": "markdown", "metadata": {"id": "execute_training"}, "source": ["## 🚀 Execute Training for All Models with Progress Tracking"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "train_all_models"}, "outputs": [], "source": ["# Train models for primary timeframes (M15, H1, H4) with enhanced progress tracking\n", "primary_timeframes = ['M15', 'H1', 'H4']\n", "trained_models = {}\n", "\n", "print(\"🎯 Starting enhanced training for all models with progress tracking...\\n\")\n", "print(\"📊 Features:\")\n", "print(\"   ✅ Real-time progress bars\")\n", "print(\"   ✅ Live training metrics\")\n", "print(\"   ✅ Time estimation\")\n", "print(\"   ✅ Interactive widgets\")\n", "print(\"   ✅ Training curve visualization\\n\")\n", "\n", "for tf in primary_timeframes:\n", "    if tf not in training_data:\n", "        print(f\"⚠️ Skipping {tf} - no training data available\")\n", "        continue\n", "    \n", "    print(f\"\\n{'='*60}\")\n", "    print(f\"🚀 Training Neural G1 models for {tf} timeframe\")\n", "    print(f\"{'='*60}\")\n", "    \n", "    data = training_data[tf]\n", "    \n", "    # 1. Train Temporal Fusion Transformer (Price Prediction) with Progress\n", "    print(f\"\\n📈 Starting TFT training for {tf}...\")\n", "    tft_model, tft_train_losses, tft_test_losses = train_tft_with_progress(\n", "        data['X_train'], data['y_price_train'],\n", "        data['X_test'], data['y_price_test'],\n", "        epochs=30, timeframe=tf\n", "    )\n", "    \n", "    print(f\"\\n\" + \"=\"*40)\n", "    \n", "    # 2. Train CNN + BiLSTM + Attention (Signal Generation) with Progress\n", "    print(f\"\\n🧠 Starting Signal Model training for {tf}...\")\n", "    signal_model, signal_train_losses, signal_test_losses, signal_train_acc, signal_test_acc = train_signal_model_with_progress(\n", "        data['X_train'], data['y_signal_train'],\n", "        data['X_test'], data['y_signal_test'],\n", "        epochs=30, timeframe=tf\n", "    )\n", "    \n", "    print(f\"\\n\" + \"=\"*40)\n", "    \n", "    # 3. Train Siamese Network (Pattern Similarity) with Progress\n", "    print(f\"\\n🔗 Starting Siamese Network training for {tf}...\")\n", "    siamese_model, siamese_losses = train_siamese_with_progress(\n", "        data['pairs_train'], data['pair_labels_train'],\n", "        epochs=20, timeframe=tf\n", "    )\n", "    \n", "    # Store trained models\n", "    trained_models[tf] = {\n", "        'tft_model': tft_model,\n", "        'signal_model': signal_model,\n", "        'siamese_model': siamese_model,\n", "        'tft_losses': {'train': tft_train_losses, 'test': tft_test_losses},\n", "        'signal_losses': {'train': signal_train_losses, 'test': signal_test_losses},\n", "        'signal_accuracies': {'train': signal_train_acc, 'test': signal_test_acc},\n", "        'siamese_losses': siamese_losses,\n", "        'scaler': scalers[tf],\n", "        'feature_cols': data['feature_cols']\n", "    }\n", "    \n", "    # Display final performance summary\n", "    print(f\"\\n\" + \"=\"*60)\n", "    print(f\"🏆 {tf} TRAINING COMPLETED - FINAL RESULTS\")\n", "    print(f\"=\"*60)\n", "    print(f\"📈 TFT Final Test Loss: {tft_test_losses[-1]:.6f}\")\n", "    print(f\"🎯 Signal Final Test Accuracy: {signal_test_acc[-1]:.4f} ({signal_test_acc[-1]*100:.2f}%)\")\n", "    print(f\"🔗 Siamese Final Loss: {siamese_losses[-1]:.4f}\")\n", "    print(f\"⏱️ Timeframe: {tf} | Models: 3/3 Complete\")\n", "    print(f\"=\"*60)\n", "\n", "print(\"\\n\" + \"🎉\"*20)\n", "print(\"🎉 ALL NEURAL G1 MODEL TRAINING COMPLETED! 🎉\")\n", "print(\"🎉\"*20)\n", "print(f\"\\n📊 Training Summary:\")\n", "print(f\"   ✅ Timeframes trained: {len(trained_models)}\")\n", "print(f\"   ✅ Total models: {len(trained_models) * 3}\")\n", "print(f\"   ✅ Model types: TFT, Signal Generator, Siamese Network\")\n", "print(f\"   ✅ All models ready for deployment!\")"]}, {"cell_type": "markdown", "metadata": {"id": "model_evaluation"}, "source": ["## 📊 Model Evaluation & Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "evaluate_models"}, "outputs": [], "source": ["# Evaluation and visualization\n", "def plot_training_history(losses_dict, title):\n", "    \"\"\"Plot training history\"\"\"\n", "    plt.figure(figsize=(12, 4))\n", "    \n", "    for tf, losses in losses_dict.items():\n", "        if 'train' in losses and 'test' in losses:\n", "            plt.subplot(1, 2, 1)\n", "            plt.plot(losses['train'], label=f'{tf} Train')\n", "            plt.plot(losses['test'], label=f'{tf} Test')\n", "            plt.title(f'{title} - Loss')\n", "            plt.xlabel('Epoch')\n", "            plt.ylabel('Loss')\n", "            plt.legend()\n", "            plt.grid(True)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def evaluate_signal_model(model, X_test, y_test, timeframe):\n", "    \"\"\"Evaluate signal generation model\"\"\"\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    model.eval()\n", "    \n", "    with torch.no_grad():\n", "        X_test_tensor = torch.FloatTensor(X_test).to(device)\n", "        outputs = model(X_test_tensor)\n", "        _, predicted = torch.max(outputs, 1)\n", "        predicted = predicted.cpu().numpy()\n", "    \n", "    # Classification report\n", "    print(f\"\\n📊 {timeframe} Signal Model Evaluation:\")\n", "    print(classification_report(y_test, predicted, target_names=['Sell', 'Hold', 'Buy']))\n", "    \n", "    # Confusion matrix\n", "    cm = confusion_matrix(y_test, predicted)\n", "    plt.figure(figsize=(8, 6))\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "                xticklabels=['Sell', 'Hold', 'Buy'],\n", "                yticklabels=['Sell', 'Hold', 'Buy'])\n", "    plt.title(f'{timeframe} - Confusion Matrix')\n", "    plt.ylabel('True Label')\n", "    plt.xlabel('Predicted Label')\n", "    plt.show()\n", "    \n", "    return predicted\n", "\n", "# Evaluate all trained models\n", "print(\"📊 Evaluating trained models...\\n\")\n", "\n", "tft_losses = {}\n", "signal_losses = {}\n", "\n", "for tf, models in trained_models.items():\n", "    print(f\"\\n{'='*40}\")\n", "    print(f\"📊 Evaluating {tf} models\")\n", "    print(f\"{'='*40}\")\n", "    \n", "    # Collect losses for plotting\n", "    tft_losses[tf] = models['tft_losses']\n", "    signal_losses[tf] = models['signal_losses']\n", "    \n", "    # Evaluate signal model\n", "    test_data = training_data[tf]\n", "    predictions = evaluate_signal_model(\n", "        models['signal_model'],\n", "        test_data['X_test'],\n", "        test_data['y_signal_test'],\n", "        tf\n", "    )\n", "\n", "# Plot training histories\n", "print(\"\\n📈 Plotting training histories...\")\n", "plot_training_history(tft_losses, \"Temporal Fusion Transformer\")\n", "plot_training_history(signal_losses, \"Signal Generation Model\")\n", "\n", "print(\"\\n✅ Model evaluation completed!\")"]}, {"cell_type": "markdown", "metadata": {"id": "save_models"}, "source": ["## 💾 Save Trained Models"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "save_trained_models"}, "outputs": [], "source": ["# Save all trained models\n", "import os\n", "from datetime import datetime\n", "\n", "# Create save directory\n", "save_dir = '/content/drive/MyDrive/Neural_G1/trained_models'\n", "os.makedirs(save_dir, exist_ok=True)\n", "\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "\n", "print(f\"💾 Saving trained models to {save_dir}...\\n\")\n", "\n", "for tf, models in trained_models.items():\n", "    tf_dir = os.path.join(save_dir, f'{tf}_{timestamp}')\n", "    os.makedirs(tf_dir, exist_ok=True)\n", "    \n", "    # Save PyTorch models\n", "    torch.save(models['tft_model'].state_dict(), \n", "               os.path.join(tf_dir, 'tft_model.pth'))\n", "    torch.save(models['signal_model'].state_dict(), \n", "               os.path.join(tf_dir, 'signal_model.pth'))\n", "    torch.save(models['siamese_model'].state_dict(), \n", "               os.path.join(tf_dir, 'siamese_model.pth'))\n", "    \n", "    # Save scaler\n", "    with open(os.path.join(tf_dir, 'scaler.pkl'), 'wb') as f:\n", "        pickle.dump(models['scaler'], f)\n", "    \n", "    # Save feature columns\n", "    with open(os.path.join(tf_dir, 'feature_cols.json'), 'w') as f:\n", "        json.dump(models['feature_cols'], f)\n", "    \n", "    # Save training history\n", "    training_history = {\n", "        'tft_losses': models['tft_losses'],\n", "        'signal_losses': models['signal_losses'],\n", "        'signal_accuracies': models['signal_accuracies'],\n", "        'siamese_losses': models['siamese_losses']\n", "    }\n", "    \n", "    with open(os.path.join(tf_dir, 'training_history.json'), 'w') as f:\n", "        json.dump(training_history, f)\n", "    \n", "    # Save model architecture info\n", "    model_info = {\n", "        'timeframe': tf,\n", "        'timestamp': timestamp,\n", "        'input_size': len(models['feature_cols']),\n", "        'sequence_length': 60,\n", "        'num_classes': 3,\n", "        'models': {\n", "            'tft': 'TemporalFusionTransformer',\n", "            'signal': 'CNNBiLSTMAttention',\n", "            'siamese': 'SiameseNetwork'\n", "        }\n", "    }\n", "    \n", "    with open(os.path.join(tf_dir, 'model_info.json'), 'w') as f:\n", "        json.dump(model_info, f, indent=2)\n", "    \n", "    print(f\"✅ {tf} models saved to {tf_dir}\")\n", "\n", "# Create a summary file\n", "summary = {\n", "    'training_date': timestamp,\n", "    'timeframes_trained': list(trained_models.keys()),\n", "    'models_per_timeframe': ['TFT', 'CNN+BiLSTM+Attention', 'SiameseNetwork'],\n", "    'total_models': len(trained_models) * 3,\n", "    'save_directory': save_dir,\n", "    'performance_summary': {}\n", "}\n", "\n", "for tf, models in trained_models.items():\n", "    summary['performance_summary'][tf] = {\n", "        'tft_final_test_loss': float(models['tft_losses']['test'][-1]),\n", "        'signal_final_test_accuracy': float(models['signal_accuracies']['test'][-1]),\n", "        'siamese_final_loss': float(models['siamese_losses'][-1])\n", "    }\n", "\n", "with open(os.path.join(save_dir, f'training_summary_{timestamp}.json'), 'w') as f:\n", "    json.dump(summary, f, indent=2)\n", "\n", "print(f\"\\n🎉 All models saved successfully!\")\n", "print(f\"📁 Save directory: {save_dir}\")\n", "print(f\"📊 Training summary saved as: training_summary_{timestamp}.json\")\n", "print(f\"\\n📋 Summary:\")\n", "print(f\"   - Timeframes trained: {', '.join(trained_models.keys())}\")\n", "print(f\"   - Total models: {len(trained_models) * 3}\")\n", "print(f\"   - Models per timeframe: TFT, CNN+BiLSTM+Attention, SiameseNetwork\")\n", "\n", "# Display final performance summary\n", "print(f\"\\n📊 Final Performance Summary:\")\n", "for tf, perf in summary['performance_summary'].items():\n", "    print(f\"   {tf}:\")\n", "    print(f\"     - TFT Test Loss: {perf['tft_final_test_loss']:.6f}\")\n", "    print(f\"     - Signal Test Accuracy: {perf['signal_final_test_accuracy']:.4f}\")\n", "    print(f\"     - Siamese Loss: {perf['siamese_final_loss']:.4f}\")\n", "\n", "print(\"\\n🚀 Neural G1 training pipeline completed successfully!\")\n", "print(\"\\n📝 Next steps:\")\n", "print(\"   1. Deploy models to production environment\")\n", "print(\"   2. Set up real-time data pipeline\")\n", "print(\"   3. Implement Telegram notification system\")\n", "print(\"   4. Create web dashboard for monitoring\")\n", "print(\"   5. Enable online learning pipeline\")"]}, {"cell_type": "markdown", "metadata": {"id": "progress_features_summary"}, "source": ["## 🎯 Training Progress Features Summary\n", "\n", "### 📊 **Enhanced Progress Tracking Features Added:**\n", "\n", "#### **1. 📈 Real-Time Progress Bars**\n", "- **Epoch Progress**: Shows overall training progress across epochs\n", "- **Batch Progress**: Shows progress within each epoch\n", "- **Color-coded bars**: Green for success, blue for info, orange for warnings\n", "- **Dynamic updates**: Progress bars update in real-time during training\n", "\n", "#### **2. ⏱️ Time Tracking & Estimation**\n", "- **Elapsed Time**: Shows total time spent training\n", "- **Remaining Time**: Estimates time left based on current pace\n", "- **Time per Epoch**: Calculates average training time per epoch\n", "- **ETA Updates**: Continuously updates estimated completion time\n", "\n", "#### **3. 📊 Live Metrics Display**\n", "- **Training Loss**: Real-time training loss updates\n", "- **Validation Loss**: Real-time validation loss updates\n", "- **Accuracy Tracking**: Live accuracy metrics for classification models\n", "- **Performance Trends**: Visual indicators for improving/declining performance\n", "\n", "#### **4. 🎨 Interactive Widgets**\n", "- **HTML Widgets**: Rich formatted progress displays\n", "- **Color-coded Metrics**: Easy-to-read performance indicators\n", "- **Responsive Layout**: Adapts to different screen sizes\n", "- **Professional Styling**: Clean, modern progress interface\n", "\n", "#### **5. 📈 Live Training Curves**\n", "- **Real-time Plotting**: Training curves update during training\n", "- **Loss Visualization**: Both training and validation loss curves\n", "- **Accuracy Plots**: Training and validation accuracy trends\n", "- **Performance Analysis**: Easy identification of overfitting/underfitting\n", "\n", "#### **6. 🏆 Final Performance Summary**\n", "- **Completion Notifications**: Clear training completion indicators\n", "- **Best Performance**: Highlights best achieved metrics\n", "- **Final Results**: Comprehensive performance summary\n", "- **Model Comparison**: Easy comparison across timeframes\n", "\n", "### 🚀 **How to Use:**\n", "\n", "1. **Run the notebook** in Google Colab with GPU enabled\n", "2. **Watch real-time progress** as models train with interactive widgets\n", "3. **Monitor performance** through live updating metrics and plots\n", "4. **Track time estimates** to plan your training sessions\n", "5. **Review final results** with comprehensive performance summaries\n", "\n", "### 💡 **Benefits:**\n", "\n", "- ✅ **No more guessing** - See exactly how training is progressing\n", "- ✅ **Time management** - Know when training will complete\n", "- ✅ **Early detection** - Spot issues before wasting compute time\n", "- ✅ **Professional interface** - Clean, informative progress displays\n", "- ✅ **Better insights** - Understand model performance in real-time\n", "\n", "---\n", "\n", "## 🎉 **Ready to Train Neural G1!**\n", "\n", "Your comprehensive AI training notebook is now equipped with:\n", "- 🧠 **5 Advanced AI Models** (TFT, CNN+BiLSTM+Attention, ViT, Siamese, Confidence Synthesizer)\n", "- 📊 **Complete Progress Tracking** (Real-time bars, metrics, time estimation)\n", "- 🎯 **Professional Interface** (Interactive widgets, live plots, summaries)\n", "- 💾 **Automatic Saving** (Models, scalers, training history)\n", "- 📈 **Performance Analysis** (Evaluation, visualization, comparison)\n", "\n", "**Start training your Neural G1 models now and watch the magic happen! 🚀**"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}